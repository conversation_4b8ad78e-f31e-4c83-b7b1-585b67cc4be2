import pandas as pd

def check_acc4_file():
    """检查ACC=4文件的结构"""
    
    file_path = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250731_232707.csv"
    
    print("="*80)
    print("检查ACC=4文件结构")
    print("="*80)
    
    try:
        # 读取文件
        df = pd.read_csv(file_path)
        print(f"✓ 成功读取文件: {file_path}")
        print(f"数据形状: {df.shape}")
        
        print(f"\n列名:")
        for i, col in enumerate(df.columns):
            print(f"{i+1:2d}. '{col}'")
        
        print(f"\n前5行数据:")
        print(df.head())
        
        print(f"\n数据类型:")
        print(df.dtypes)
        
        # 检查是否有Group相关的列
        group_cols = [col for col in df.columns if 'group' in col.lower() or 'Group' in col]
        if group_cols:
            print(f"\nGroup相关列: {group_cols}")
            for col in group_cols:
                print(f"{col}: {df[col].nunique()} 个唯一值")
                print(f"范围: {df[col].min()} - {df[col].max()}")
        else:
            print(f"\n⚠️ 未找到Group相关列")
        
        # 检查是否有流量相关的列
        flow_cols = [col for col in df.columns if 'flow' in col.lower() or 'Flow' in col or 'rate' in col.lower()]
        if flow_cols:
            print(f"\n流量相关列: {flow_cols}")
        
        # 检查是否有体积相关的列
        volume_cols = [col for col in df.columns if 'volume' in col.lower() or 'Volume' in col]
        if volume_cols:
            print(f"\n体积相关列: {volume_cols}")
        
        # 检查是否有ACH相关的列
        ach_cols = [col for col in df.columns if 'ach' in col.lower() or 'ACH' in col]
        if ach_cols:
            print(f"\nACH相关列: {ach_cols}")
        
        return df
        
    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return None

if __name__ == "__main__":
    check_acc4_file()
