import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_improved_ach_plot():
    """创建改进的ACH网格细化变化图 - 均匀X轴，黑灰红配色"""
    
    print("="*80)
    print("创建改进的ACH网格细化变化图")
    print("特点: 均匀X轴分布 + 黑灰红配色方案")
    print("="*80)
    
    try:
        # 读取三网格收敛性分析数据
        df = pd.read_csv('three_mesh_convergence_no_group456.csv')
        print(f"✓ 读取收敛性分析数据: {len(df)} 个Group")
    except Exception as e:
        print(f"✗ 读取数据失败: {e}")
        return
    
    # 指定要保留的Group ID
    selected_groups = [0, 3, 11, 12, 29, 1, 2, 8, 10, 14, 15, 16, 17, 18, 19, 24, 30]
    print(f"选择的Group ID: {selected_groups}")
    
    # 准备数据
    groups = []
    ach_coarse = []  # ACC=1 粗网格
    ach_medium = []  # ACC=2 中网格
    ach_fine = []    # ACC=3 细网格
    
    for _, row in df.iterrows():
        group_id = int(row['Group'])
        
        # 只保留指定的Group
        if group_id in selected_groups:
            # 获取ACH值
            ach_1 = row.get('accbuilding_1_no_group456_ACH', np.nan)  # 粗
            ach_2 = row.get('accbuilding_2_no_group456_ACH', np.nan)  # 中
            ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)  # 细
            
            if not (np.isnan(ach_1) or np.isnan(ach_2) or np.isnan(ach_3)):
                groups.append(group_id)
                ach_coarse.append(ach_1)
                ach_medium.append(ach_2)
                ach_fine.append(ach_3)
    
    # 按Group ID排序
    sorted_data = sorted(zip(groups, ach_coarse, ach_medium, ach_fine))
    groups, ach_coarse, ach_medium, ach_fine = zip(*sorted_data)
    
    print(f"✓ 成功提取 {len(groups)} 个Group的数据")
    print(f"Group列表: {list(groups)}")
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(16, 8))
    
    # 创建均匀分布的x轴位置
    x_positions = range(len(groups))  # 0, 1, 2, 3, ... 均匀分布
    
    # 定义黑灰红配色方案
    colors = {
        'coarse': '#2C2C2C',    # 深灰色/黑色
        'medium': '#808080',    # 中灰色
        'fine': '#DC143C'       # 深红色
    }
    
    # 绘制三条线 - 使用黑灰红配色方案
    line1 = ax.plot(x_positions, ach_coarse, 'o-', linewidth=3, markersize=10, 
                    color=colors['coarse'], alpha=0.9, label='ACC=1 (粗网格)', 
                    markerfacecolor='white', markeredgewidth=2.5, markeredgecolor=colors['coarse'])
    
    line2 = ax.plot(x_positions, ach_medium, 's--', linewidth=3, markersize=10, 
                    color=colors['medium'], alpha=0.9, label='ACC=2 (中网格)', 
                    markerfacecolor='white', markeredgewidth=2.5, markeredgecolor=colors['medium'])
    
    line3 = ax.plot(x_positions, ach_fine, '^-', linewidth=3, markersize=10, 
                    color=colors['fine'], alpha=0.9, label='ACC=3 (细网格)', 
                    markerfacecolor='white', markeredgewidth=2.5, markeredgecolor=colors['fine'])
    
    # 设置坐标轴
    ax.set_xlabel('Group ID', fontsize=16, fontweight='bold')
    ax.set_ylabel('ACH (h⁻¹)', fontsize=16, fontweight='bold')
    ax.set_title('ACH在网格细化过程中的变化', fontsize=18, fontweight='bold', pad=25)
    
    # 设置x轴刻度 - 均匀分布但显示实际Group ID
    ax.set_xticks(x_positions)
    ax.set_xticklabels(groups, fontsize=14, fontweight='bold')
    
    # 设置y轴
    ax.tick_params(axis='y', labelsize=14)
    
    # 添加网格 - 使用浅灰色
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.8, color='#CCCCCC')
    ax.set_axisbelow(True)
    
    # 添加图例 - 改进样式
    legend = ax.legend(fontsize=14, loc='upper left', frameon=True, fancybox=True, 
                      shadow=True, borderpad=1, columnspacing=1)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_alpha(0.95)
    legend.get_frame().set_edgecolor('#CCCCCC')
    legend.get_frame().set_linewidth(1.5)
    
    # 设置图表边框
    for spine in ax.spines.values():
        spine.set_linewidth(2)
        spine.set_color('#666666')
    
    # 移除数值标注 - 保持图表简洁
    # excellent_groups = [0, 3, 11, 12, 29]
    # for i, group_id in enumerate(groups):
    #     if group_id in excellent_groups:
    #         # 在细网格点上标注数值
    #         ax.annotate(f'{ach_fine[i]:.1f}',
    #                    xy=(x_positions[i], ach_fine[i]),
    #                    xytext=(8, 12), textcoords='offset points',
    #                    fontsize=11, ha='left', va='bottom', fontweight='bold',
    #                    bbox=dict(boxstyle='round,pad=0.4', facecolor='#FFE4E1',
    #                             edgecolor=colors['fine'], alpha=0.8, linewidth=1.5),
    #                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.1',
    #                                  color=colors['fine'], lw=1.5))
    
    # 设置y轴范围，留出更多空间
    y_min = min(min(ach_coarse), min(ach_medium), min(ach_fine))
    y_max = max(max(ach_coarse), max(ach_medium), max(ach_fine))
    y_range = y_max - y_min
    ax.set_ylim(y_min - y_range * 0.05, y_max + y_range * 0.15)
    
    # 移除副标题说明
    # ax.text(0.02, 0.98, '注: 标注数值为收敛良好Group的ACC=3结果',
    #         transform=ax.transAxes, fontsize=12, va='top', ha='left',
    #         bbox=dict(boxstyle='round,pad=0.5', facecolor='#F0F0F0', alpha=0.8))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('improved_ach_grid_refinement.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    print(f"📊 改进的ACH网格细化变化图已保存: improved_ach_grid_refinement.png")
    
    # 显示详细统计信息
    print(f"\n📊 详细数据统计:")
    print(f"{'序号':<4} {'Group':<6} {'ACC=1':<8} {'ACC=2':<8} {'ACC=3':<8} {'粗→中%':<8} {'中→细%':<8} {'独立性':<10}")
    print("-" * 70)
    
    for i, group_id in enumerate(groups):
        change_1_2 = abs(ach_medium[i] - ach_coarse[i]) / ach_coarse[i] * 100 if ach_coarse[i] != 0 else 0
        change_2_3 = abs(ach_fine[i] - ach_medium[i]) / ach_medium[i] * 100 if ach_medium[i] != 0 else 0
        
        # 判断网格独立性
        if change_2_3 < 5:
            independence = "✅ 独立"
        elif change_2_3 < 10:
            independence = "⚠️ 边界"
        else:
            independence = "❌ 需细化"
        
        print(f"{i+1:<4} {group_id:<6} {ach_coarse[i]:<8.2f} {ach_medium[i]:<8.2f} {ach_fine[i]:<8.2f} "
              f"{change_1_2:<8.1f} {change_2_3:<8.1f} {independence:<10}")
    
    # 统计汇总
    total_groups = len(groups)
    independent_count = sum(1 for i in range(len(groups)) 
                           if abs(ach_fine[i] - ach_medium[i]) / ach_medium[i] * 100 < 5)
    borderline_count = sum(1 for i in range(len(groups)) 
                          if 5 <= abs(ach_fine[i] - ach_medium[i]) / ach_medium[i] * 100 < 10)
    
    print(f"\n📈 网格独立性汇总:")
    print(f"• 总Group数: {total_groups}")
    print(f"• 网格独立 (<5%): {independent_count} ({independent_count/total_groups*100:.1f}%)")
    print(f"• 边界情况 (5-10%): {borderline_count} ({borderline_count/total_groups*100:.1f}%)")
    print(f"• 需要细化 (>10%): {total_groups-independent_count-borderline_count} ({(total_groups-independent_count-borderline_count)/total_groups*100:.1f}%)")
    
    return fig

def main():
    """主函数"""
    
    print("开始创建改进的ACH网格细化变化图...")
    
    # 创建改进的ACH变化图
    create_improved_ach_plot()
    
    print(f"\n{'='*80}")
    print("改进的ACH网格细化变化图创建完成！")
    print("改进特点:")
    print("• ✅ X轴均匀分布 - 消除了Group ID间隔不均的问题")
    print("• 🎨 黑灰红配色方案 - 专业的学术风格")
    print("• 📊 增强的视觉效果 - 更大的标记和线宽")
    print("• 🎯 简洁的图表设计 - 无数字标注，清晰易读")
    print("• 📈 完整的统计信息 - 网格独立性分析")
    print("生成文件: improved_ach_grid_refinement.png")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
