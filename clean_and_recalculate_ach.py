import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

def detect_outliers_advanced(data, method='iqr', threshold=1.5):
    """高级异常值检测"""
    if len(data) <= 1:
        return np.zeros(len(data), dtype=bool)
    
    outliers = np.zeros(len(data), dtype=bool)
    
    if method == 'iqr':
        Q1 = np.percentile(data, 25)
        Q3 = np.percentile(data, 75)
        IQR = Q3 - Q1
        if IQR > 0:
            lower_bound = Q1 - threshold * IQR
            upper_bound = Q3 + threshold * IQR
            outliers = (data < lower_bound) | (data > upper_bound)
    
    elif method == 'zscore':
        if np.std(data) > 0:
            z_scores = np.abs(stats.zscore(data))
            outliers = z_scores > threshold
    
    elif method == 'modified_zscore':
        median = np.median(data)
        mad = np.median(np.abs(data - median))
        if mad > 0:
            modified_z_scores = 0.6745 * (data - median) / mad
            outliers = np.abs(modified_z_scores) > threshold
    
    elif method == 'combined':
        # 组合方法：同时使用IQR和Modified Z-score
        outliers_iqr = detect_outliers_advanced(data, 'iqr', threshold)
        outliers_mz = detect_outliers_advanced(data, 'modified_zscore', threshold)
        outliers = outliers_iqr | outliers_mz
    
    return outliers

def analyze_and_clean_group(group_data, group_id, outlier_method='combined', outlier_threshold=1.5):
    """分析并清理单个Group的数据"""
    
    flow_rates = group_data['FlowRate'].values
    areas = group_data['Area'].values if 'Area' in group_data.columns else np.ones(len(flow_rates))
    volume = group_data['Volume'].iloc[0]
    original_ach = group_data['ACH'].iloc[0] if 'ACH' in group_data.columns else None
    
    # 使用绝对值进行异常值检测
    abs_flow_rates = np.abs(flow_rates)
    
    # 检测异常值
    outliers = detect_outliers_advanced(abs_flow_rates, method=outlier_method, threshold=outlier_threshold)
    outlier_count = np.sum(outliers)
    
    # 确保至少保留一定比例的探针
    min_probes = max(1, int(len(flow_rates) * 0.3))  # 至少保留30%的探针
    if len(flow_rates) - outlier_count < min_probes:
        # 如果异常值太多，使用更宽松的阈值
        outliers = detect_outliers_advanced(abs_flow_rates, method=outlier_method, threshold=outlier_threshold * 1.5)
        outlier_count = np.sum(outliers)
        
        if len(flow_rates) - outlier_count < min_probes:
            # 如果还是太多，只移除最极端的异常值
            sorted_indices = np.argsort(abs_flow_rates)
            keep_count = max(min_probes, len(flow_rates) - int(len(flow_rates) * 0.2))  # 最多移除20%
            outliers = np.ones(len(flow_rates), dtype=bool)
            outliers[sorted_indices[:keep_count]] = False
            outlier_count = np.sum(outliers)
    
    # 清理数据
    clean_flow_rates = flow_rates[~outliers]
    clean_areas = areas[~outliers]
    
    # 计算多种ACH方法
    ach_methods = {}
    
    # 方法1: 平均绝对流率
    Q1 = np.mean(np.abs(clean_flow_rates)) * 3600
    ach_methods['mean'] = Q1 / volume if volume != 0 else 0
    
    # 方法2: 中位数方法（最robust）
    Q2 = np.median(np.abs(clean_flow_rates)) * 3600
    ach_methods['median'] = Q2 / volume if volume != 0 else 0
    
    # 方法3: 面积加权平均
    if len(clean_areas) == len(clean_flow_rates) and np.sum(clean_areas) > 0:
        weighted_flow = np.average(np.abs(clean_flow_rates), weights=clean_areas)
        Q3 = weighted_flow * 3600
        ach_methods['weighted'] = Q3 / volume if volume != 0 else 0
    else:
        ach_methods['weighted'] = ach_methods['mean']
    
    # 方法4: 总体积流率
    if len(clean_areas) == len(clean_flow_rates):
        Q4 = np.sum(np.abs(clean_flow_rates * clean_areas)) * 3600
        ach_methods['total_volume'] = Q4 / volume if volume != 0 else 0
    else:
        ach_methods['total_volume'] = ach_methods['mean']
    
    # 方法5: 截断平均（去除10%极值）
    sorted_flows = np.sort(np.abs(clean_flow_rates))
    trim_percent = 0.1
    trim_count = int(len(sorted_flows) * trim_percent)
    if trim_count > 0 and len(sorted_flows) > 2 * trim_count:
        trimmed_flows = sorted_flows[trim_count:-trim_count]
    else:
        trimmed_flows = sorted_flows
    Q5 = np.mean(trimmed_flows) * 3600
    ach_methods['trimmed'] = Q5 / volume if volume != 0 else 0
    
    # 选择推荐的ACH值（使用中位数方法）
    recommended_ach = ach_methods['median']
    
    # 计算改善程度
    improvement_info = {}
    if original_ach is not None and original_ach != 0:
        improvement_info['original_ach'] = original_ach
        improvement_info['change_percent'] = (recommended_ach - original_ach) / original_ach * 100
    
    return {
        'group_id': group_id,
        'volume': volume,
        'total_probes': len(flow_rates),
        'outliers_removed': outlier_count,
        'valid_probes': len(clean_flow_rates),
        'outlier_percentage': outlier_count / len(flow_rates) * 100,
        'ach_methods': ach_methods,
        'recommended_ach': recommended_ach,
        'improvement_info': improvement_info,
        'flow_rate_stats': {
            'original_min': np.min(flow_rates),
            'original_max': np.max(flow_rates),
            'original_std': np.std(flow_rates),
            'clean_min': np.min(clean_flow_rates),
            'clean_max': np.max(clean_flow_rates),
            'clean_std': np.std(clean_flow_rates)
        }
    }

def clean_and_recalculate_csv(input_csv_path, output_csv_path, mesh_name="accbuilding_2", 
                             outlier_method='combined', outlier_threshold=1.5):
    """清理CSV数据并重新计算ACH"""
    
    print(f"{'='*80}")
    print(f"数据清理和ACH重新计算")
    print(f"网格: {mesh_name}")
    print(f"输入文件: {input_csv_path}")
    print(f"输出文件: {output_csv_path}")
    print(f"异常值检测方法: {outlier_method}")
    print(f"异常值阈值: {outlier_threshold}")
    print(f"{'='*80}")
    
    # 读取原始数据
    try:
        df = pd.read_csv(input_csv_path)
        print(f"✓ 成功读取数据，共 {len(df)} 个探针点，{df['GroupID'].nunique()} 个Group")
    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return None
    
    # 按Group处理数据
    grouped = df.groupby('GroupID')
    cleaned_results = []
    summary_stats = []
    
    print(f"\n🔧 开始数据清理...")
    print(f"{'Group':<6} {'原探针':<8} {'移除':<6} {'保留':<6} {'异常%':<8} {'原ACH':<12} {'新ACH':<12} {'变化%':<10}")
    print("-" * 85)
    
    for group_id, group_data in grouped:
        result = analyze_and_clean_group(group_data, group_id, outlier_method, outlier_threshold)
        
        # 输出处理结果
        original_ach = result['improvement_info'].get('original_ach', 0)
        change_percent = result['improvement_info'].get('change_percent', 0)
        
        print(f"{group_id:<6} {result['total_probes']:<8} {result['outliers_removed']:<6} "
              f"{result['valid_probes']:<6} {result['outlier_percentage']:<8.1f} "
              f"{original_ach:<12.6f} {result['recommended_ach']:<12.6f} {change_percent:<10.2f}")
        
        # 创建清理后的数据行
        clean_group_data = group_data[~detect_outliers_advanced(
            np.abs(group_data['FlowRate'].values), outlier_method, outlier_threshold)].copy()
        
        # 确保至少保留30%的探针
        min_probes = max(1, int(len(group_data) * 0.3))
        if len(clean_group_data) < min_probes:
            # 保留流率最小的探针
            sorted_indices = np.argsort(np.abs(group_data['FlowRate'].values))
            keep_indices = sorted_indices[:min_probes]
            clean_group_data = group_data.iloc[keep_indices].copy()
        
        # 更新ACH值
        clean_group_data['ACH'] = result['recommended_ach']
        cleaned_results.append(clean_group_data)
        
        # 保存统计信息
        summary_stats.append({
            'GroupID': group_id,
            'Volume': result['volume'],
            'Total_Probes': result['total_probes'],
            'Outliers_Removed': result['outliers_removed'],
            'Valid_Probes': result['valid_probes'],
            'Outlier_Percentage': result['outlier_percentage'],
            'Original_ACH': original_ach,
            'New_ACH': result['recommended_ach'],
            'Change_Percent': change_percent,
            'ACH_Mean': result['ach_methods']['mean'],
            'ACH_Median': result['ach_methods']['median'],
            'ACH_Weighted': result['ach_methods']['weighted'],
            'ACH_Total_Volume': result['ach_methods']['total_volume'],
            'ACH_Trimmed': result['ach_methods']['trimmed']
        })
    
    # 合并清理后的数据
    cleaned_df = pd.concat(cleaned_results, ignore_index=True)
    
    # 保存清理后的CSV
    cleaned_df.to_csv(output_csv_path, index=False)
    
    # 保存统计报告
    summary_df = pd.DataFrame(summary_stats)
    summary_report_path = output_csv_path.replace('.csv', '_cleaning_report.csv')
    summary_df.to_csv(summary_report_path, index=False)
    
    # 输出总结
    total_original = len(df)
    total_cleaned = len(cleaned_df)
    total_removed = total_original - total_cleaned
    removal_rate = total_removed / total_original * 100
    
    print(f"\n📊 清理总结:")
    print(f"  原始探针数: {total_original}")
    print(f"  清理后探针数: {total_cleaned}")
    print(f"  移除探针数: {total_removed}")
    print(f"  移除比例: {removal_rate:.2f}%")
    
    # ACH变化统计
    ach_changes = [s['Change_Percent'] for s in summary_stats if s['Original_ACH'] != 0]
    if ach_changes:
        print(f"  ACH变化统计:")
        print(f"    平均变化: {np.mean(ach_changes):.2f}%")
        print(f"    最大变化: {np.max(np.abs(ach_changes)):.2f}%")
        print(f"    标准差: {np.std(ach_changes):.2f}%")
    
    print(f"\n💾 生成文件:")
    print(f"  - {output_csv_path}: 清理后的数据")
    print(f"  - {summary_report_path}: 清理统计报告")
    
    return cleaned_df, summary_df

def create_cleaning_visualization(summary_df, mesh_name):
    """创建数据清理可视化"""
    
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle(f'{mesh_name} 数据清理分析', fontsize=16, fontweight='bold')
    
    # 1. 异常值移除比例
    axes[0,0].bar(summary_df['GroupID'], summary_df['Outlier_Percentage'], 
                  alpha=0.7, color='lightcoral')
    axes[0,0].set_xlabel('Group ID')
    axes[0,0].set_ylabel('异常值比例 (%)')
    axes[0,0].set_title('各Group异常值移除比例')
    axes[0,0].tick_params(axis='x', rotation=45)
    axes[0,0].grid(True, alpha=0.3)
    
    # 2. ACH变化对比
    x = np.arange(len(summary_df))
    width = 0.35
    axes[0,1].bar(x - width/2, summary_df['Original_ACH'], width, 
                  label='原始ACH', alpha=0.7, color='lightblue')
    axes[0,1].bar(x + width/2, summary_df['New_ACH'], width, 
                  label='清理后ACH', alpha=0.7, color='lightgreen')
    axes[0,1].set_xlabel('Group ID')
    axes[0,1].set_ylabel('ACH')
    axes[0,1].set_title('ACH值对比')
    axes[0,1].set_xticks(x)
    axes[0,1].set_xticklabels(summary_df['GroupID'], rotation=45)
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # 3. ACH变化百分比
    valid_changes = summary_df[summary_df['Original_ACH'] != 0]
    if not valid_changes.empty:
        axes[1,0].bar(valid_changes['GroupID'], valid_changes['Change_Percent'], 
                      alpha=0.7, color='orange')
        axes[1,0].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        axes[1,0].set_xlabel('Group ID')
        axes[1,0].set_ylabel('ACH变化 (%)')
        axes[1,0].set_title('ACH变化百分比')
        axes[1,0].tick_params(axis='x', rotation=45)
        axes[1,0].grid(True, alpha=0.3)
    
    # 4. 探针数量对比
    axes[1,1].bar(summary_df['GroupID'], summary_df['Total_Probes'], 
                  alpha=0.5, label='原始探针', color='gray')
    axes[1,1].bar(summary_df['GroupID'], summary_df['Valid_Probes'], 
                  alpha=0.7, label='有效探针', color='green')
    axes[1,1].set_xlabel('Group ID')
    axes[1,1].set_ylabel('探针数量')
    axes[1,1].set_title('探针数量对比')
    axes[1,1].legend()
    axes[1,1].tick_params(axis='x', rotation=45)
    axes[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    plot_file = f"{mesh_name}_cleaning_analysis.png"
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"📊 清理分析图表已保存: {plot_file}")
    
    plt.show()

def main():
    """主函数"""
    
    # 设置参数
    input_csv = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250731_150834.csv"
    output_csv = "accbuilding_2_cleaned.csv"
    mesh_name = "accbuilding_2"
    
    print("开始清理accbuilding_2数据...")
    
    # 执行数据清理
    cleaned_df, summary_df = clean_and_recalculate_csv(
        input_csv, output_csv, mesh_name, 
        outlier_method='combined', outlier_threshold=1.5
    )
    
    if cleaned_df is not None and summary_df is not None:
        # 创建可视化
        create_cleaning_visualization(summary_df, mesh_name)
        
        print(f"\n{'='*80}")
        print(f"数据清理完成！")
        print(f"现在可以使用清理后的数据进行网格敏感性分析")
        print(f"{'='*80}")

if __name__ == "__main__":
    main()
