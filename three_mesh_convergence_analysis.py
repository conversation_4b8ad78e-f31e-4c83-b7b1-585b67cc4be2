import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from itertools import combinations

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_mesh_data(file_paths):
    """加载多个网格数据"""
    mesh_data = {}
    
    for mesh_name, file_path in file_paths.items():
        try:
            df = pd.read_csv(file_path)
            # 按Group聚合数据
            group_summary = df.groupby('GroupID').agg({
                'Volume': 'first',
                'ACH': 'first',
                'FlowRate': 'count'  # 计算探针数量
            }).rename(columns={'FlowRate': 'Probe_Count'}).reset_index()
            
            mesh_data[mesh_name] = group_summary
            print(f"✓ 读取 {mesh_name}: {len(df)} 个探针点，{len(group_summary)} 个Group")
            
        except Exception as e:
            print(f"✗ 读取 {mesh_name} 失败: {e}")
            continue
    
    return mesh_data

def calculate_convergence_three_mesh(mesh_data):
    """计算三个网格的收敛性"""
    
    # 获取所有网格的Group列表
    all_groups = set()
    for data in mesh_data.values():
        all_groups.update(data['GroupID'].tolist())
    all_groups = sorted(list(all_groups))
    
    convergence_results = []
    
    for group_id in all_groups:
        group_results = {'Group': group_id}
        
        # 收集该Group在所有网格中的数据
        group_ach_values = {}
        group_volume = None
        
        for mesh_name, data in mesh_data.items():
            group_data = data[data['GroupID'] == group_id]
            if not group_data.empty:
                ach_value = group_data['ACH'].iloc[0]
                group_ach_values[mesh_name] = ach_value
                if group_volume is None:
                    group_volume = group_data['Volume'].iloc[0]
                group_results[f'{mesh_name}_ACH'] = ach_value
                group_results[f'{mesh_name}_Probes'] = group_data['Probe_Count'].iloc[0]
        
        group_results['Volume'] = group_volume
        
        # 计算收敛性指标
        if len(group_ach_values) >= 2:
            ach_values = list(group_ach_values.values())
            min_ach = min(ach_values)
            max_ach = max(ach_values)
            mean_ach = np.mean(ach_values)
            
            # 计算相对变化百分比
            if mean_ach != 0:
                relative_change = (max_ach - min_ach) / mean_ach * 100
            else:
                relative_change = 0
            
            group_results['Min_ACH'] = min_ach
            group_results['Max_ACH'] = max_ach
            group_results['Mean_ACH'] = mean_ach
            group_results['Relative_Change_%'] = relative_change
            group_results['Converged'] = relative_change < 5.0  # 5%收敛标准
            
            # 计算网格间的变化率
            mesh_names = list(group_ach_values.keys())
            if len(mesh_names) == 3:
                # 三个网格：计算相邻网格间的变化
                sorted_meshes = sorted(mesh_names, key=lambda x: int(x.split('_')[1]))  # 按网格编号排序
                
                for i in range(len(sorted_meshes) - 1):
                    mesh1, mesh2 = sorted_meshes[i], sorted_meshes[i+1]
                    ach1, ach2 = group_ach_values[mesh1], group_ach_values[mesh2]
                    
                    if ach1 != 0:
                        change_rate = abs(ach2 - ach1) / ach1 * 100
                        group_results[f'Change_{mesh1}_to_{mesh2}_%'] = change_rate
            
            group_results['Meshes'] = ', '.join(mesh_names)
        
        convergence_results.append(group_results)
    
    return pd.DataFrame(convergence_results)

def analyze_convergence_trend(convergence_df):
    """分析收敛趋势 - 按照网格精度从粗到细排序"""

    print(f"\n🎯 三网格收敛性分析 (从粗到细):")
    print(f"{'Group':<6} {'Volume':<10} {'ACC=1(粗)':<12} {'ACC=2(中)':<12} {'ACC=3(细)':<12} {'变化%':<10} {'收敛':<8} {'趋势':<8}")
    print("-" * 90)

    converged_count = 0
    total_groups = len(convergence_df)
    monotonic_count = 0

    for _, row in convergence_df.iterrows():
        group_id = int(row['Group'])
        volume = row['Volume'] if pd.notna(row['Volume']) else 0

        # 获取ACH值 - 按照从粗到细的顺序
        ach_1 = row.get('accbuilding_1_no_group6_ACH', np.nan)  # 粗网格
        ach_2 = row.get('accbuilding_2_no_group6_ACH', np.nan)  # 中等网格
        ach_3 = row.get('accbuilding_3_no_group6_ACH', np.nan)  # 细网格

        relative_change = row.get('Relative_Change_%', 0)
        converged = row.get('Converged', False)

        # 分析收敛趋势
        trend = "未知"
        if not (np.isnan(ach_1) or np.isnan(ach_2) or np.isnan(ach_3)):
            # 检查是否单调收敛
            diff_1_2 = abs(ach_2 - ach_1)
            diff_2_3 = abs(ach_3 - ach_2)

            if diff_2_3 < diff_1_2:
                trend = "收敛✓"
                if converged:
                    monotonic_count += 1
            elif diff_2_3 > diff_1_2 * 1.5:
                trend = "发散✗"
            else:
                trend = "振荡~"

        if converged:
            converged_count += 1
            status = "✅"
        else:
            status = "❌"

        print(f"{group_id:<6} {volume:<10.1f} {ach_1:<12.3f} {ach_2:<12.3f} {ach_3:<12.3f} "
              f"{relative_change:<10.2f} {status:<8} {trend:<8}")

    convergence_rate = converged_count / total_groups * 100 if total_groups > 0 else 0
    monotonic_rate = monotonic_count / total_groups * 100 if total_groups > 0 else 0

    print(f"\n📊 收敛性统计:")
    print(f"  总Group数: {total_groups}")
    print(f"  已收敛Group数: {converged_count}")
    print(f"  收敛率: {convergence_rate:.1f}%")
    print(f"  单调收敛Group数: {monotonic_count}")
    print(f"  单调收敛率: {monotonic_rate:.1f}%")

    return convergence_rate, monotonic_rate

def plot_convergence_analysis(convergence_df, mesh_data):
    """绘制收敛性分析图表"""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('三网格收敛性分析', fontsize=16, fontweight='bold')
    
    # 1. ACH值比较 - 按照网格精度从粗到细
    ax1 = axes[0, 0]
    groups = convergence_df['Group'].values

    if 'accbuilding_1_no_group6_ACH' in convergence_df.columns:
        ach_1 = convergence_df['accbuilding_1_no_group6_ACH'].values
        ax1.plot(groups, ach_1, '^-', label='ACC=1 (粗网格)', linewidth=2, markersize=6, color='red')

    if 'accbuilding_2_no_group6_ACH' in convergence_df.columns:
        ach_2 = convergence_df['accbuilding_2_no_group6_ACH'].values
        ax1.plot(groups, ach_2, 's-', label='ACC=2 (中等网格)', linewidth=2, markersize=6, color='orange')

    if 'accbuilding_3_no_group6_ACH' in convergence_df.columns:
        ach_3 = convergence_df['accbuilding_3_no_group6_ACH'].values
        ax1.plot(groups, ach_3, 'o-', label='ACC=3 (细网格)', linewidth=2, markersize=6, color='green')
    
    ax1.set_xlabel('Group ID')
    ax1.set_ylabel('ACH (次/小时)')
    ax1.set_title('网格细化过程中ACH值变化 (粗→中→细)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 相对变化百分比
    ax2 = axes[0, 1]
    relative_changes = convergence_df['Relative_Change_%'].values
    colors = ['green' if x < 5 else 'orange' if x < 15 else 'red' for x in relative_changes]
    
    bars = ax2.bar(groups, relative_changes, color=colors, alpha=0.7)
    ax2.axhline(y=5, color='red', linestyle='--', alpha=0.8, label='5%收敛线')
    ax2.set_xlabel('Group ID')
    ax2.set_ylabel('相对变化 (%)')
    ax2.set_title('各Group的网格收敛性')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 收敛性分布
    ax3 = axes[1, 0]
    converged_count = sum(convergence_df['Converged'])
    not_converged_count = len(convergence_df) - converged_count
    
    labels = ['已收敛 (<5%)', '未收敛 (≥5%)']
    sizes = [converged_count, not_converged_count]
    colors = ['lightgreen', 'lightcoral']
    
    wedges, texts, autotexts = ax3.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax3.set_title('收敛性分布')
    
    # 4. ACH值分布直方图
    ax4 = axes[1, 1]
    
    all_ach_values = []
    mesh_labels = []
    
    for mesh_name in ['accbuilding_3_no_group6', 'accbuilding_2_no_group6', 'accbuilding_1_no_group6']:
        ach_col = f'{mesh_name}_ACH'
        if ach_col in convergence_df.columns:
            ach_values = convergence_df[ach_col].dropna().values
            all_ach_values.extend(ach_values)
            mesh_labels.extend([mesh_name.split('_')[1]] * len(ach_values))
    
    if all_ach_values:
        # 创建分组直方图
        unique_meshes = list(set(mesh_labels))
        for i, mesh in enumerate(unique_meshes):
            mesh_ach = [all_ach_values[j] for j, label in enumerate(mesh_labels) if label == mesh]
            ax4.hist(mesh_ach, bins=15, alpha=0.7, label=f'accbuilding={mesh}', 
                    color=plt.cm.Set1(i))
    
    ax4.set_xlabel('ACH (次/小时)')
    ax4.set_ylabel('频次')
    ax4.set_title('ACH值分布')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('three_mesh_convergence_analysis.png', dpi=300, bbox_inches='tight')
    print(f"📊 三网格收敛性图表已保存: three_mesh_convergence_analysis.png")
    
    return fig

def main():
    """主函数"""
    
    print("开始三网格敏感性分析...")
    
    # 定义三个网格数据文件
    mesh_files = {
        'accbuilding_3_no_group6': "accbuilding_3_no_group6.csv",
        'accbuilding_2_no_group6': "accbuilding_2_no_group6.csv",
        'accbuilding_1_no_group6': "accbuilding_1_no_group6.csv"
    }
    
    print(f"比较网格: {list(mesh_files.keys())}")
    print("="*80)
    
    # 加载数据
    mesh_data = load_mesh_data(mesh_files)
    
    if len(mesh_data) < 2:
        print("❌ 至少需要两个有效的网格数据文件")
        return
    
    print("="*80)
    print("三网格收敛性分析结果")
    print("="*80)
    
    # 计算收敛性
    convergence_df = calculate_convergence_three_mesh(mesh_data)
    
    # 分析收敛趋势
    convergence_rate, monotonic_rate = analyze_convergence_trend(convergence_df)
    
    # 保存详细结果
    convergence_df.to_csv('three_mesh_convergence_analysis.csv', index=False)
    
    # 识别问题Group
    problem_groups = convergence_df[convergence_df['Relative_Change_%'] > 15].sort_values('Relative_Change_%', ascending=False)
    
    if not problem_groups.empty:
        print(f"\n⚠️  需要重点关注的Group (变化>15%):")
        for _, row in problem_groups.head(10).iterrows():
            group_id = int(row['Group'])
            change = row['Relative_Change_%']
            print(f"    Group {group_id}: 变化 {change:.2f}%")
    
    # 绘制图表
    plot_convergence_analysis(convergence_df, mesh_data)
    
    print(f"\n💾 结果已保存:")
    print(f"  - three_mesh_convergence_analysis.csv: 详细收敛性分析")
    print(f"  - three_mesh_convergence_analysis.png: 可视化图表")
    
    print(f"\n🎯 网格敏感性分析总结:")
    print(f"  网格收敛率: {convergence_rate:.1f}%")
    print(f"  单调收敛率: {monotonic_rate:.1f}%")

    if convergence_rate >= 50:
        print(f"  ✅ 网格质量良好，大部分区域已收敛")
        if monotonic_rate >= 40:
            print(f"  ✅ 网格细化趋势良好，解单调收敛")
        else:
            print(f"  ⚠️  部分区域存在振荡，建议检查数值格式")
    elif convergence_rate >= 30:
        print(f"  ⚠️  网格质量可接受，建议继续细化")
        if monotonic_rate < 20:
            print(f"  ⚠️  收敛趋势不稳定，建议检查边界条件")
    else:
        print(f"  ❌ 网格敏感性强，需要显著细化网格")
        print(f"  📋 建议: 继续细化至ACC=4或更高精度")

    print(f"\n📈 网格细化建议:")
    if convergence_rate < 30:
        print(f"  🔧 全面细化: 所有区域都需要更精细的网格")
    elif monotonic_rate < convergence_rate * 0.7:
        print(f"  🔧 局部优化: 重点关注振荡区域的网格质量")
    else:
        print(f"  🔧 选择性细化: 仅对未收敛区域进行细化")

if __name__ == "__main__":
    main()
