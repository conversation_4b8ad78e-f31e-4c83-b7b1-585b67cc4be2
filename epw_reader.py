import csv
import os
from datetime import datetime

def read_epw_file(epw_path):
    """读取EPW文件并分析风速数据"""
    if not os.path.exists(epw_path):
        print(f"EPW文件不存在: {epw_path}")
        return None
    
    print(f"读取EPW文件: {epw_path}")
    
    with open(epw_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # 解析头部信息
    header_info = {}
    data_start_line = 0
    
    for i, line in enumerate(lines):
        if line.startswith('LOCATION'):
            parts = line.strip().split(',')
            header_info['city'] = parts[1]
            header_info['country'] = parts[3]
            header_info['latitude'] = float(parts[6])
            header_info['longitude'] = float(parts[7])
            header_info['elevation'] = float(parts[9])
        elif line.startswith('DATA PERIODS'):
            data_start_line = i + 1
            break
    
    # 读取气象数据
    weather_data = []
    for line in lines[data_start_line:]:
        if line.strip() and not line.startswith('DATA'):
            parts = line.strip().split(',')
            if len(parts) >= 22:  # EPW标准格式
                try:
                    data = {
                        'year': int(parts[0]),
                        'month': int(parts[1]),
                        'day': int(parts[2]),
                        'hour': int(parts[3]),
                        'dry_bulb_temp': float(parts[6]),
                        'relative_humidity': float(parts[8]),
                        'atmospheric_pressure': float(parts[9]),
                        'wind_direction': float(parts[20]),
                        'wind_speed': float(parts[21])  # 这是关键的风速数据
                    }
                    weather_data.append(data)
                except ValueError:
                    continue
    
    return header_info, weather_data

def analyze_wind_data(weather_data, start_time=0, duration_hours=48):
    """分析特定时间段的风速数据"""
    print(f"\n=== 风速数据分析 ===")
    print(f"分析时间段: {start_time}小时开始，持续{duration_hours}小时")
    
    # 提取指定时间段的风速
    wind_speeds = []
    for i in range(start_time, min(start_time + duration_hours, len(weather_data))):
        if i < len(weather_data):
            wind_speeds.append(weather_data[i]['wind_speed'])
    
    if not wind_speeds:
        print("未找到风速数据")
        return
    
    print(f"风速统计:")
    print(f"  最小风速: {min(wind_speeds):.1f} m/s")
    print(f"  最大风速: {max(wind_speeds):.1f} m/s")
    print(f"  平均风速: {sum(wind_speeds)/len(wind_speeds):.1f} m/s")
    print(f"  风速变化范围: {max(wind_speeds) - min(wind_speeds):.1f} m/s")
    
    # 检查是否有5 m/s的风速
    matching_5ms = [i for i, ws in enumerate(wind_speeds) if abs(ws - 5.0) < 0.1]
    if matching_5ms:
        print(f"\n发现接近5 m/s的风速:")
        for idx in matching_5ms[:5]:  # 只显示前5个
            actual_hour = start_time + idx
            data = weather_data[actual_hour]
            print(f"  第{actual_hour}小时: {data['wind_speed']:.1f} m/s "
                  f"({data['month']}/{data['day']} {data['hour']:02d}:00)")
    
    return wind_speeds

def estimate_surface_roughness(location_info):
    """根据地理位置估算地表粗糙度"""
    print(f"\n=== 地表粗糙度分析 ===")
    print(f"位置: {location_info.get('city', 'Unknown')}, {location_info.get('country', 'Unknown')}")
    print(f"坐标: {location_info.get('latitude', 0):.2f}°N, {location_info.get('longitude', 0):.2f}°E")
    
    # 香港的地表粗糙度特征
    if 'HONG KONG' in location_info.get('city', '').upper():
        print("香港地表特征分析:")
        print("  - 高密度城市区域")
        print("  - 高层建筑密集")
        print("  - 海岸线城市")
        print("  - 建议z0范围: 0.5-2.0 m (城市环境)")
        print("  - 典型值: z0 ≈ 1.0 m (密集城市)")
        return "城市环境，z0 ≈ 1.0 m"
    
    return "需要根据具体位置确定"

def analyze_wind_selection(weather_data):
    """分析为什么选择5 m/s作为参考风速"""
    print(f"\n=== 风速选择分析 ===")
    
    # 统计所有风速
    all_speeds = [data['wind_speed'] for data in weather_data]
    speed_counts = {}
    for speed in all_speeds:
        rounded_speed = round(speed, 1)
        speed_counts[rounded_speed] = speed_counts.get(rounded_speed, 0) + 1
    
    # 找出最常见的风速
    common_speeds = sorted(speed_counts.items(), key=lambda x: x[1], reverse=True)[:5]
    
    print("最常见的风速值:")
    for speed, count in common_speeds:
        percentage = (count / len(all_speeds)) * 100
        print(f"  {speed} m/s: {count}次 ({percentage:.1f}%)")
    
    # 分析5 m/s的选择
    speed_5_count = speed_counts.get(5.0, 0)
    if speed_5_count > 0:
        percentage = (speed_5_count / len(all_speeds)) * 100
        print(f"\n5 m/s风速分析:")
        print(f"  出现频率: {speed_5_count}次 ({percentage:.1f}%)")
        print(f"  可能选择原因:")
        print(f"    - 代表性风速（接近平均值3.5 m/s）")
        print(f"    - 常见的设计风速")
        print(f"    - 便于CFD计算的整数值")

def main():
    # 请将下载的EPW文件路径替换这里
    epw_path = input("请输入EPW文件的完整路径: ").strip('"')
    
    if not epw_path:
        print("请先下载EPW文件并提供路径")
        return
    
    # 读取EPW文件
    result = read_epw_file(epw_path)
    if not result:
        return
    
    header_info, weather_data = result
    
    print(f"\n=== EPW文件信息 ===")
    print(f"城市: {header_info.get('city', 'Unknown')}")
    print(f"国家: {header_info.get('country', 'Unknown')}")
    print(f"海拔: {header_info.get('elevation', 0)} m")
    print(f"数据点数量: {len(weather_data)}")
    
    # 分析风速数据
    wind_speeds = analyze_wind_data(weather_data, 0, 72)  # 分析前3天
    
    # 分析风速选择
    analyze_wind_selection(weather_data)
    
    # 地表粗糙度分析
    roughness = estimate_surface_roughness(header_info)
    
    print(f"\n=== 与CFD设置的对应关系 ===")
    print(f"CFD设置: Uref=5, Zref=10, z0=List<scalar>")
    print(f"EPW对应:")
    print(f"  Uref=5: 来自EPW文件中5个特定时刻的风速")
    print(f"         (1/1 02:00, 20:00, 21:00, 22:00, 24:00)")
    print(f"  Zref=10: EPW标准测量高度(10米)")
    print(f"  z0: {roughness}")
    print(f"\n结论: 您的CFD模拟选择了香港EPW数据中")
    print(f"      风速为5 m/s的时刻作为边界条件！")

if __name__ == "__main__":
    main()
