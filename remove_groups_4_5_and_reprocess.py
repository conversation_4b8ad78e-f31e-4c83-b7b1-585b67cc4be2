import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def remove_groups_4_5_from_all_meshes():
    """从所有三个网格中删除Group 4和Group 5"""
    
    print("="*80)
    print("删除Group 4和Group 5 - 不属于优化范畴的flat")
    print("处理三个网格: ACC=1(粗), ACC=2(中), ACC=3(细)")
    print("="*80)
    
    # 定义输入和输出文件
    mesh_files = {
        'accbuilding_1_no_group6': {
            'input': 'accbuilding_1_no_group6.csv',
            'output': 'accbuilding_1_no_group456.csv'
        },
        'accbuilding_2_no_group6': {
            'input': 'accbuilding_2_no_group6.csv', 
            'output': 'accbuilding_2_no_group456.csv'
        },
        'accbuilding_3_no_group6': {
            'input': 'accbuilding_3_no_group6.csv',
            'output': 'accbuilding_3_no_group456.csv'
        }
    }
    
    processing_summary = []
    
    for mesh_name, files in mesh_files.items():
        print(f"\n🔧 处理 {mesh_name}:")
        print(f"  输入文件: {files['input']}")
        print(f"  输出文件: {files['output']}")
        
        try:
            # 读取数据
            df = pd.read_csv(files['input'])
            original_groups = df['GroupID'].nunique()
            original_probes = len(df)
            
            print(f"  ✓ 读取成功: {original_probes} 个探针点，{original_groups} 个Group")
            
            # 统计要删除的Group信息
            groups_to_remove = [4, 5]
            removed_info = []
            
            for group_id in groups_to_remove:
                if group_id in df['GroupID'].values:
                    group_data = df[df['GroupID'] == group_id]
                    group_probes = len(group_data)
                    group_volume = group_data['Volume'].iloc[0]
                    removed_info.append({
                        'group_id': group_id,
                        'probes': group_probes,
                        'volume': group_volume
                    })
                    print(f"  🗑️  删除Group {group_id}: {group_probes} 个探针点，体积: {group_volume:.1f} m³")
            
            # 删除Group 4和5
            df_filtered = df[~df['GroupID'].isin(groups_to_remove)].copy()
            
            remaining_groups = df_filtered['GroupID'].nunique()
            remaining_probes = len(df_filtered)
            total_removed_probes = original_probes - remaining_probes
            
            print(f"  📊 删除统计:")
            print(f"    原始Group数: {original_groups}")
            print(f"    剩余Group数: {remaining_groups}")
            print(f"    原始探针数: {original_probes}")
            print(f"    剩余探针数: {remaining_probes}")
            print(f"    删除探针数: {total_removed_probes}")
            
            # 保存处理后的数据
            df_filtered.to_csv(files['output'], index=False)
            print(f"  ✅ 保存成功: {files['output']}")
            
            # 记录处理摘要
            processing_summary.append({
                'mesh_name': mesh_name,
                'original_groups': original_groups,
                'remaining_groups': remaining_groups,
                'original_probes': original_probes,
                'remaining_probes': remaining_probes,
                'removed_probes': total_removed_probes,
                'removed_groups': removed_info
            })
            
        except Exception as e:
            print(f"  ✗ 处理失败: {e}")
            continue
    
    # 输出总体统计
    print(f"\n📊 总体处理统计:")
    print(f"{'网格':<20} {'原Group':<10} {'剩余Group':<10} {'原探针':<10} {'剩余探针':<10} {'删除探针':<10}")
    print("-" * 80)
    
    for summary in processing_summary:
        print(f"{summary['mesh_name']:<20} {summary['original_groups']:<10} "
              f"{summary['remaining_groups']:<10} {summary['original_probes']:<10} "
              f"{summary['remaining_probes']:<10} {summary['removed_probes']:<10}")
    
    print(f"\n🎯 删除Group 4和5完成！")
    print(f"现在您有三个处理后的数据文件:")
    for files in mesh_files.values():
        print(f"  - {files['output']}")
    
    return processing_summary

def update_convergence_analysis():
    """更新收敛性分析，使用删除Group 4和5后的数据"""
    
    print(f"\n🔄 更新三网格收敛性分析...")
    
    # 定义新的网格文件
    mesh_files = {
        'accbuilding_3_no_group456': "accbuilding_3_no_group456.csv",
        'accbuilding_2_no_group456': "accbuilding_2_no_group456.csv", 
        'accbuilding_1_no_group456': "accbuilding_1_no_group456.csv"
    }
    
    mesh_data = {}
    
    # 加载数据
    for mesh_name, file_path in mesh_files.items():
        try:
            df = pd.read_csv(file_path)
            # 按Group聚合数据
            group_summary = df.groupby('GroupID').agg({
                'Volume': 'first',
                'ACH': 'first',
                'FlowRate': 'count'  # 计算探针数量
            }).rename(columns={'FlowRate': 'Probe_Count'}).reset_index()
            
            mesh_data[mesh_name] = group_summary
            print(f"✓ 读取 {mesh_name}: {len(df)} 个探针点，{len(group_summary)} 个Group")
            
        except Exception as e:
            print(f"✗ 读取 {mesh_name} 失败: {e}")
            continue
    
    if len(mesh_data) < 2:
        print("❌ 至少需要两个有效的网格数据文件")
        return
    
    # 计算收敛性
    all_groups = set()
    for data in mesh_data.values():
        all_groups.update(data['GroupID'].tolist())
    all_groups = sorted(list(all_groups))
    
    convergence_results = []
    
    for group_id in all_groups:
        group_results = {'Group': group_id}
        
        # 收集该Group在所有网格中的数据
        group_ach_values = {}
        group_volume = None
        
        for mesh_name, data in mesh_data.items():
            group_data = data[data['GroupID'] == group_id]
            if not group_data.empty:
                ach_value = group_data['ACH'].iloc[0]
                group_ach_values[mesh_name] = ach_value
                if group_volume is None:
                    group_volume = group_data['Volume'].iloc[0]
                group_results[f'{mesh_name}_ACH'] = ach_value
                group_results[f'{mesh_name}_Probes'] = group_data['Probe_Count'].iloc[0]
        
        group_results['Volume'] = group_volume
        
        # 计算收敛性指标
        if len(group_ach_values) >= 2:
            ach_values = list(group_ach_values.values())
            min_ach = min(ach_values)
            max_ach = max(ach_values)
            mean_ach = np.mean(ach_values)
            
            # 计算相对变化百分比
            if mean_ach != 0:
                relative_change = (max_ach - min_ach) / mean_ach * 100
            else:
                relative_change = 0
            
            group_results['Min_ACH'] = min_ach
            group_results['Max_ACH'] = max_ach
            group_results['Mean_ACH'] = mean_ach
            group_results['Relative_Change_%'] = relative_change
            group_results['Converged'] = relative_change < 5.0  # 5%收敛标准
            
            group_results['Meshes'] = ', '.join(group_ach_values.keys())
        
        convergence_results.append(group_results)
    
    convergence_df = pd.DataFrame(convergence_results)
    
    # 保存更新的收敛性分析
    convergence_df.to_csv('three_mesh_convergence_no_group456.csv', index=False)
    
    # 分析收敛趋势
    print(f"\n🎯 更新后的三网格收敛性分析 (删除Group 4,5后):")
    print(f"{'Group':<6} {'Volume':<10} {'ACC=1(粗)':<12} {'ACC=2(中)':<12} {'ACC=3(细)':<12} {'变化%':<10} {'收敛':<8}")
    print("-" * 85)
    
    converged_count = 0
    total_groups = len(convergence_df)
    
    for _, row in convergence_df.iterrows():
        group_id = int(row['Group'])
        volume = row['Volume'] if pd.notna(row['Volume']) else 0
        
        # 获取ACH值 - 按照从粗到细的顺序
        ach_1 = row.get('accbuilding_1_no_group456_ACH', np.nan)  # 粗网格
        ach_2 = row.get('accbuilding_2_no_group456_ACH', np.nan)  # 中等网格
        ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)  # 细网格
        
        relative_change = row.get('Relative_Change_%', 0)
        converged = row.get('Converged', False)
        
        if converged:
            converged_count += 1
            status = "✅"
        else:
            status = "❌"
        
        print(f"{group_id:<6} {volume:<10.1f} {ach_1:<12.3f} {ach_2:<12.3f} {ach_3:<12.3f} "
              f"{relative_change:<10.2f} {status:<8}")
    
    convergence_rate = converged_count / total_groups * 100 if total_groups > 0 else 0
    
    print(f"\n📊 更新后的收敛性统计:")
    print(f"  总Group数: {total_groups} (删除Group 4,5后)")
    print(f"  已收敛Group数: {converged_count}")
    print(f"  收敛率: {convergence_rate:.1f}%")
    
    # 识别问题Group
    problem_groups = convergence_df[convergence_df['Relative_Change_%'] > 15].sort_values('Relative_Change_%', ascending=False)
    
    if not problem_groups.empty:
        print(f"\n⚠️  需要重点关注的Group (变化>15%):")
        for _, row in problem_groups.head(10).iterrows():
            group_id = int(row['Group'])
            change = row['Relative_Change_%']
            print(f"    Group {group_id}: 变化 {change:.2f}%")
    
    print(f"\n💾 更新的分析结果已保存:")
    print(f"  - three_mesh_convergence_no_group456.csv: 删除Group 4,5后的收敛性分析")
    
    return convergence_rate

def main():
    """主函数"""
    
    print("开始删除Group 4和Group 5...")
    
    # 删除Group 4和5
    processing_summary = remove_groups_4_5_from_all_meshes()
    
    if processing_summary:
        # 更新收敛性分析
        convergence_rate = update_convergence_analysis()
        
        print(f"\n{'='*80}")
        print("删除Group 4和5完成！")
        print(f"更新后的网格收敛率: {convergence_rate:.1f}%")
        print("现在可以基于优化范畴内的Group进行网格敏感性分析")
        print(f"{'='*80}")
        
        return True
    else:
        print("删除操作失败！")
        return False

if __name__ == "__main__":
    main()
