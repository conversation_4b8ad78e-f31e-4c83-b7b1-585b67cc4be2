import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_mesh_convergence(mesh_data_dict):
    """分析网格收敛性"""
    
    print(f"{'='*80}")
    print(f"网格敏感性分析 - 收敛性评估")
    print(f"{'='*80}")
    
    # 处理每个网格的数据
    mesh_results = {}
    
    for mesh_name, csv_path in mesh_data_dict.items():
        print(f"\n📊 分析网格: {mesh_name}")
        print(f"数据文件: {csv_path}")
        
        try:
            df = pd.read_csv(csv_path)
            print(f"✓ 读取成功，共 {len(df)} 个探针点，{df['GroupID'].nunique()} 个Group")
            
            # 按Group计算ACH
            grouped = df.groupby('GroupID')
            group_results = {}
            
            for group_id, group_data in grouped:
                # 基本信息
                volume = group_data['Volume'].iloc[0]
                flow_rates = group_data['FlowRate'].values
                
                # 异常值检测和清理
                abs_flow_rates = np.abs(flow_rates)
                Q1 = np.percentile(abs_flow_rates, 25)
                Q3 = np.percentile(abs_flow_rates, 75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                outliers = (abs_flow_rates < lower_bound) | (abs_flow_rates > upper_bound)
                
                # 清理数据
                if np.sum(outliers) > 0 and len(flow_rates) - np.sum(outliers) >= 1:
                    clean_flow_rates = flow_rates[~outliers]
                else:
                    clean_flow_rates = flow_rates
                
                # 计算ACH (使用中位数方法)
                Q_median = np.median(np.abs(clean_flow_rates)) * 3600
                ach = Q_median / volume if volume != 0 else 0
                
                group_results[group_id] = {
                    'volume': volume,
                    'total_probes': len(flow_rates),
                    'valid_probes': len(clean_flow_rates),
                    'outliers_removed': np.sum(outliers),
                    'ach': ach
                }
            
            mesh_results[mesh_name] = group_results
            
        except Exception as e:
            print(f"✗ 读取失败: {e}")
            continue
    
    # 创建比较表格
    print(f"\n{'='*80}")
    print(f"网格收敛性分析结果")
    print(f"{'='*80}")
    
    # 获取所有Group ID
    all_groups = set()
    for mesh_results_dict in mesh_results.values():
        all_groups.update(mesh_results_dict.keys())
    all_groups = sorted(all_groups)
    
    # 创建比较数据
    comparison_data = []
    convergence_data = []
    
    for group_id in all_groups:
        group_ach_values = {}
        group_info = {}
        
        # 收集每个网格的ACH值
        for mesh_name, mesh_data in mesh_results.items():
            if group_id in mesh_data:
                group_ach_values[mesh_name] = mesh_data[group_id]['ach']
                group_info = mesh_data[group_id]  # 保存最后一个网格的信息
        
        # 如果至少有两个网格有这个Group的数据
        if len(group_ach_values) >= 2:
            ach_values = list(group_ach_values.values())
            mesh_names = list(group_ach_values.keys())
            
            # 计算收敛性指标
            max_ach = max(ach_values)
            min_ach = min(ach_values)
            mean_ach = np.mean(ach_values)
            
            # 相对变化百分比
            if min_ach != 0:
                relative_change = (max_ach - min_ach) / min_ach * 100
            else:
                relative_change = 999.99 if max_ach != 0 else 0  # 用大数值代替无穷大
            
            # 判断收敛性
            is_converged = relative_change < 5.0  # 5%阈值
            
            # 添加到比较数据
            for mesh_name, ach_value in group_ach_values.items():
                comparison_data.append({
                    'Group': group_id,
                    'Mesh': mesh_name,
                    'Volume': group_info['volume'],
                    'Valid_Probes': group_info['valid_probes'],
                    'ACH': ach_value
                })
            
            # 添加到收敛数据
            convergence_data.append({
                'Group': group_id,
                'Volume': group_info['volume'],
                'Min_ACH': min_ach,
                'Max_ACH': max_ach,
                'Mean_ACH': mean_ach,
                'Relative_Change_%': relative_change,
                'Converged': is_converged,
                'Meshes': ', '.join(mesh_names)
            })
    
    # 转换为DataFrame
    comparison_df = pd.DataFrame(comparison_data)
    convergence_df = pd.DataFrame(convergence_data)
    
    # 输出结果
    print(f"\n📋 网格比较详细结果:")
    if not comparison_df.empty:
        # 按Group和Mesh排序
        comparison_df_sorted = comparison_df.sort_values(['Group', 'Mesh'])
        print(comparison_df_sorted.to_string(index=False))
    
    print(f"\n🎯 收敛性分析:")
    if not convergence_df.empty:
        print(convergence_df.to_string(index=False))
        
        # 统计收敛情况
        converged_count = convergence_df['Converged'].sum()
        total_count = len(convergence_df)
        convergence_rate = converged_count / total_count * 100
        
        print(f"\n📊 收敛性统计:")
        print(f"  总Group数: {total_count}")
        print(f"  已收敛Group数: {converged_count}")
        print(f"  收敛率: {convergence_rate:.1f}%")
        
        # 分析未收敛的Group
        non_converged = convergence_df[~convergence_df['Converged']]
        if not non_converged.empty:
            print(f"\n⚠️  未收敛的Group:")
            for _, row in non_converged.iterrows():
                print(f"    Group {row['Group']}: 变化 {row['Relative_Change_%']:.2f}%")
        
        # 分析变化最大的Group
        top_changes = convergence_df.nlargest(5, 'Relative_Change_%')
        print(f"\n📈 变化最大的5个Group:")
        for _, row in top_changes.iterrows():
            status = "✓ 已收敛" if row['Converged'] else "⚠️ 未收敛"
            print(f"    Group {row['Group']}: {row['Relative_Change_%']:.2f}% {status}")
    
    # 保存结果
    comparison_df.to_csv('mesh_comparison_detailed.csv', index=False)
    convergence_df.to_csv('mesh_convergence_analysis.csv', index=False)
    
    print(f"\n💾 结果已保存:")
    print(f"  - mesh_comparison_detailed.csv: 详细比较数据")
    print(f"  - mesh_convergence_analysis.csv: 收敛性分析")
    
    return comparison_df, convergence_df

def create_convergence_plots(comparison_df, convergence_df):
    """创建收敛性可视化图表"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('网格敏感性分析 - 收敛性评估', fontsize=16, fontweight='bold')
    
    # 1. 各Group的ACH比较（选择前20个Group）
    if not comparison_df.empty:
        top_groups = comparison_df['Group'].unique()[:20]  # 只显示前20个Group
        subset_df = comparison_df[comparison_df['Group'].isin(top_groups)]
        
        pivot_data = subset_df.pivot(index='Group', columns='Mesh', values='ACH')
        pivot_data.plot(kind='bar', ax=axes[0,0], width=0.8)
        axes[0,0].set_title('各Group ACH值比较 (前20个Group)')
        axes[0,0].set_xlabel('Group ID')
        axes[0,0].set_ylabel('ACH')
        axes[0,0].legend(title='网格')
        axes[0,0].tick_params(axis='x', rotation=45)
        axes[0,0].grid(True, alpha=0.3)
    
    # 2. 相对变化分布
    if not convergence_df.empty:
        axes[0,1].hist(convergence_df['Relative_Change_%'], bins=20, alpha=0.7, 
                      color='skyblue', edgecolor='black')
        axes[0,1].axvline(x=5, color='red', linestyle='--', label='5% 收敛阈值')
        axes[0,1].set_xlabel('相对变化 (%)')
        axes[0,1].set_ylabel('Group数量')
        axes[0,1].set_title('ACH相对变化分布')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
    
    # 3. 体积 vs 相对变化
    if not convergence_df.empty:
        colors = ['green' if conv else 'red' for conv in convergence_df['Converged']]
        scatter = axes[1,0].scatter(convergence_df['Volume'], convergence_df['Relative_Change_%'], 
                                   c=colors, alpha=0.6)
        axes[1,0].axhline(y=5, color='red', linestyle='--', label='5% 收敛阈值')
        axes[1,0].set_xlabel('体积 (m³)')
        axes[1,0].set_ylabel('相对变化 (%)')
        axes[1,0].set_title('体积 vs ACH相对变化')
        axes[1,0].legend(['5% 阈值', '已收敛', '未收敛'])
        axes[1,0].grid(True, alpha=0.3)
    
    # 4. 收敛性饼图
    if not convergence_df.empty:
        converged_count = convergence_df['Converged'].sum()
        non_converged_count = len(convergence_df) - converged_count
        
        labels = ['已收敛', '未收敛']
        sizes = [converged_count, non_converged_count]
        colors = ['lightgreen', 'lightcoral']
        
        axes[1,1].pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        axes[1,1].set_title('网格收敛性统计')
    
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('mesh_convergence_plots.png', dpi=300, bbox_inches='tight')
    print(f"📊 收敛性图表已保存: mesh_convergence_plots.png")
    
    plt.show()

def generate_recommendations(convergence_df):
    """生成网格敏感性分析建议"""
    
    print(f"\n{'='*80}")
    print(f"网格敏感性分析建议")
    print(f"{'='*80}")
    
    if convergence_df.empty:
        print("无法生成建议：缺少收敛性数据")
        return
    
    converged_count = convergence_df['Converged'].sum()
    total_count = len(convergence_df)
    convergence_rate = converged_count / total_count * 100
    
    print(f"\n🎯 总体评估:")
    print(f"  收敛率: {convergence_rate:.1f}% ({converged_count}/{total_count})")
    
    if convergence_rate >= 90:
        print(f"  ✅ 网格收敛性优秀，当前网格精度已足够")
    elif convergence_rate >= 70:
        print(f"  ⚠️  网格收敛性良好，但建议进一步细化")
    else:
        print(f"  ❌ 网格收敛性不足，需要显著提高网格精度")
    
    # 分析未收敛的Group
    non_converged = convergence_df[~convergence_df['Converged']]
    if not non_converged.empty:
        print(f"\n🔍 需要关注的区域:")
        
        # 按变化程度排序
        worst_groups = non_converged.nlargest(5, 'Relative_Change_%')
        for _, row in worst_groups.iterrows():
            print(f"  Group {row['Group']}: 变化 {row['Relative_Change_%']:.2f}% (体积: {row['Volume']:.1f} m³)")
    
    # 网格建议
    print(f"\n📋 下一步建议:")
    
    if convergence_rate < 70:
        print(f"  1. 🔧 需要更精细的网格 (建议 accbuilding_4 或更高)")
        print(f"  2. 🎯 重点细化未收敛区域的网格")
        print(f"  3. 📊 增加探针密度以提高数据质量")
    elif convergence_rate < 90:
        print(f"  1. 🔧 可选择性细化网格 (accbuilding_4)")
        print(f"  2. 🎯 重点关注变化较大的Group")
        print(f"  3. ✅ 大部分区域已收敛，可考虑接受当前精度")
    else:
        print(f"  1. ✅ 当前网格精度已足够，可以进行后续分析")
        print(f"  2. 📊 可以基于当前结果进行工程设计")
        print(f"  3. 🔍 如需更高精度，可选择性细化特定区域")
    
    # 计算平均ACH变化
    avg_change = convergence_df['Relative_Change_%'].mean()
    max_change = convergence_df['Relative_Change_%'].max()
    
    print(f"\n📈 变化统计:")
    print(f"  平均变化: {avg_change:.2f}%")
    print(f"  最大变化: {max_change:.2f}%")
    print(f"  建议阈值: < 5% (工程标准)")

def main():
    """主函数"""

    # 定义网格数据 - 使用删除Group 6后的数据（核心筒和走廊）
    mesh_data = {
        'accbuilding_3_no_group6': "accbuilding_3_no_group6.csv",
        'accbuilding_2_no_group6': "accbuilding_2_no_group6.csv"
    }
    
    print("开始网格敏感性分析...")
    print(f"比较网格: {list(mesh_data.keys())}")
    
    # 执行收敛性分析
    comparison_df, convergence_df = analyze_mesh_convergence(mesh_data)
    
    # 创建可视化
    if not comparison_df.empty and not convergence_df.empty:
        create_convergence_plots(comparison_df, convergence_df)
    
    # 生成建议
    generate_recommendations(convergence_df)
    
    print(f"\n{'='*80}")
    print(f"分析完成！")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
