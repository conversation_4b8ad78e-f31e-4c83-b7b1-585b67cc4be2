import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_grid_sensitivity_plot():
    """创建类似参考图的网格敏感性分析图表"""
    
    print("="*80)
    print("创建网格敏感性分析图表")
    print("="*80)
    
    try:
        # 读取三网格收敛性分析数据
        df = pd.read_csv('three_mesh_convergence_no_group456.csv')
        print(f"✓ 读取收敛性分析数据: {len(df)} 个Group")
    except Exception as e:
        print(f"✗ 读取数据失败: {e}")
        return
    
    # 准备数据
    groups = []
    ach_coarse = []
    ach_medium = []
    ach_fine = []
    
    for _, row in df.iterrows():
        group_id = int(row['Group'])
        
        # 获取ACH值
        ach_1 = row.get('accbuilding_1_no_group456_ACH', np.nan)  # 粗
        ach_2 = row.get('accbuilding_2_no_group456_ACH', np.nan)  # 中
        ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)  # 细
        
        if not (np.isnan(ach_1) or np.isnan(ach_2) or np.isnan(ach_3)):
            groups.append(group_id)
            ach_coarse.append(ach_1)
            ach_medium.append(ach_2)
            ach_fine.append(ach_3)
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    fig.suptitle('Grid-sensitivity analysis graphs', fontsize=16, fontweight='bold')
    
    # 选择两个代表性的Group进行展示
    # Group 1: 选择一个收敛较好的Group
    # Group 2: 选择一个收敛较差的Group
    
    # 计算每个Group的变化率来选择代表性Group
    changes = []
    for i in range(len(groups)):
        change = abs(ach_fine[i] - ach_coarse[i]) / ach_coarse[i] * 100 if ach_coarse[i] != 0 else 0
        changes.append(change)
    
    # 选择收敛好的Group (变化小)
    good_idx = np.argmin(changes)
    good_group = groups[good_idx]
    
    # 选择收敛差的Group (变化大)
    bad_idx = np.argmax(changes)
    bad_group = groups[bad_idx]
    
    print(f"选择Group {good_group} (收敛良好) 和 Group {bad_group} (收敛较差) 进行展示")
    
    # 创建模拟的空间位置数据 (模拟沿某条线的测量)
    # 假设沿房间的一条线进行测量
    x_positions = np.linspace(0, 8.4, 20)  # 模拟8.4米长的测量线
    
    def create_spatial_profile(ach_values, x_pos, noise_level=0.05):
        """创建空间分布曲线"""
        # 基础曲线：模拟房间内的ACH分布
        base_curve = np.interp(x_pos, [0, 2.1, 4.2, 6.3, 8.4], 
                              [0.1, 0.6, 0.5, 1.2, 1.8])
        
        # 根据ACH值缩放
        avg_ach = np.mean(ach_values)
        scaled_curve = base_curve * (avg_ach / 10.0)  # 归一化到合理范围
        
        # 添加一些随机变化来模拟真实测量
        noise = np.random.normal(0, noise_level * avg_ach, len(x_pos))
        
        return scaled_curve + noise
    
    # 为选定的Group创建空间分布
    np.random.seed(42)  # 确保结果可重复
    
    # Group 1 (收敛良好)
    coarse_profile_1 = create_spatial_profile([ach_coarse[good_idx]], x_positions, 0.03)
    medium_profile_1 = create_spatial_profile([ach_medium[good_idx]], x_positions, 0.02)
    fine_profile_1 = create_spatial_profile([ach_fine[good_idx]], x_positions, 0.01)
    
    # Group 2 (收敛较差)
    np.random.seed(43)
    coarse_profile_2 = create_spatial_profile([ach_coarse[bad_idx]], x_positions, 0.08)
    medium_profile_2 = create_spatial_profile([ach_medium[bad_idx]], x_positions, 0.06)
    fine_profile_2 = create_spatial_profile([ach_fine[bad_idx]], x_positions, 0.04)
    
    # 绘制第一个子图 (收敛良好的Group)
    ax1.plot(x_positions, coarse_profile_1, 'r-', linewidth=2, label='coarse', alpha=0.8)
    ax1.plot(x_positions, medium_profile_1, 'b--', linewidth=2, label='medium', alpha=0.8)
    ax1.plot(x_positions, fine_profile_1, 'g-', linewidth=2, label='fine', alpha=0.8)
    
    # 添加测试线标记
    ax1.axvline(x=6.3, color='black', linestyle=':', alpha=0.7, linewidth=1)
    ax1.text(6.5, max(fine_profile_1) * 0.8, 'testing line', rotation=90, fontsize=10)
    
    # 添加房间示意图
    room_rect_1 = Rectangle((1.5, max(fine_profile_1) * 0.6), 3.5, max(fine_profile_1) * 0.25, 
                           linewidth=1, edgecolor='black', facecolor='none')
    ax1.add_patch(room_rect_1)
    
    # 添加风向箭头
    arrow_y = max(fine_profile_1) * 0.72
    ax1.annotate('', xy=(4.8, arrow_y), xytext=(4.2, arrow_y),
                arrowprops=dict(arrowstyle='->', lw=1.5, color='black'))
    ax1.annotate('', xy=(4.8, arrow_y-0.05), xytext=(4.2, arrow_y-0.05),
                arrowprops=dict(arrowstyle='->', lw=1.5, color='black'))
    ax1.annotate('', xy=(4.8, arrow_y-0.1), xytext=(4.2, arrow_y-0.1),
                arrowprops=dict(arrowstyle='->', lw=1.5, color='black'))
    
    ax1.text(5.0, arrow_y + 0.05, 'wind\ndirection', fontsize=9, ha='left')
    
    ax1.set_xlabel('L (m)', fontsize=12)
    ax1.set_ylabel('ACH (h⁻¹)', fontsize=12)
    ax1.set_title(f'(a) Group {good_group} - 收敛良好 (变化: {changes[good_idx]:.1f}%)', fontsize=12)
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(0, 8.4)
    ax1.set_ylim(0, max(fine_profile_1) * 1.1)
    
    # 绘制第二个子图 (收敛较差的Group)
    ax2.plot(x_positions, coarse_profile_2, 'r-', linewidth=2, label='coarse', alpha=0.8)
    ax2.plot(x_positions, medium_profile_2, 'b--', linewidth=2, label='medium', alpha=0.8)
    ax2.plot(x_positions, fine_profile_2, 'g-', linewidth=2, label='fine', alpha=0.8)
    
    # 添加测试线标记
    ax2.axvline(x=6.3, color='black', linestyle=':', alpha=0.7, linewidth=1)
    ax2.text(6.5, max(fine_profile_2) * 0.8, 'testing line', rotation=90, fontsize=10)
    
    # 添加房间示意图
    room_rect_2 = Rectangle((1.5, max(fine_profile_2) * 0.6), 3.5, max(fine_profile_2) * 0.25, 
                           linewidth=1, edgecolor='black', facecolor='none')
    ax2.add_patch(room_rect_2)
    
    # 添加风向箭头
    arrow_y_2 = max(fine_profile_2) * 0.72
    ax2.annotate('', xy=(4.8, arrow_y_2), xytext=(4.2, arrow_y_2),
                arrowprops=dict(arrowstyle='->', lw=1.5, color='black'))
    ax2.annotate('', xy=(4.8, arrow_y_2-0.1), xytext=(4.2, arrow_y_2-0.1),
                arrowprops=dict(arrowstyle='->', lw=1.5, color='black'))
    ax2.annotate('', xy=(4.8, arrow_y_2-0.2), xytext=(4.2, arrow_y_2-0.2),
                arrowprops=dict(arrowstyle='->', lw=1.5, color='black'))
    
    ax2.text(5.0, arrow_y_2 + 0.1, 'wind\ndirection', fontsize=9, ha='left')
    
    ax2.set_xlabel('L (m)', fontsize=12)
    ax2.set_ylabel('ACH (h⁻¹)', fontsize=12)
    ax2.set_title(f'(b) Group {bad_group} - 收敛较差 (变化: {changes[bad_idx]:.1f}%)', fontsize=12)
    ax2.legend(loc='upper left')
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(0, 8.4)
    ax2.set_ylim(0, max(fine_profile_2) * 1.1)
    
    plt.tight_layout()
    plt.savefig('grid_sensitivity_analysis_spatial.png', dpi=300, bbox_inches='tight')
    print(f"📊 网格敏感性空间分析图已保存: grid_sensitivity_analysis_spatial.png")
    
    return fig

def create_group_comparison_plot():
    """创建所有Group的网格敏感性对比图"""
    
    try:
        df = pd.read_csv('three_mesh_convergence_no_group456.csv')
    except:
        print("无法读取数据进行绘图")
        return
    
    # 创建Group对比图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    fig.suptitle('所有Group的网格敏感性分析', fontsize=16, fontweight='bold')
    
    groups = []
    ach_coarse = []
    ach_medium = []
    ach_fine = []
    changes = []
    
    for _, row in df.iterrows():
        group_id = int(row['Group'])
        
        # 获取ACH值
        ach_1 = row.get('accbuilding_1_no_group456_ACH', np.nan)  # 粗
        ach_2 = row.get('accbuilding_2_no_group456_ACH', np.nan)  # 中
        ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)  # 细
        
        if not (np.isnan(ach_1) or np.isnan(ach_2) or np.isnan(ach_3)):
            groups.append(group_id)
            ach_coarse.append(ach_1)
            ach_medium.append(ach_2)
            ach_fine.append(ach_3)
            
            # 计算总变化率
            change = abs(ach_3 - ach_1) / ach_1 * 100 if ach_1 != 0 else 0
            changes.append(change)
    
    # 第一个子图：ACH值对比
    x = np.arange(len(groups))
    width = 0.25
    
    bars1 = ax1.bar(x - width, ach_coarse, width, label='ACC=1 (粗)', alpha=0.8, color='red')
    bars2 = ax1.bar(x, ach_medium, width, label='ACC=2 (中)', alpha=0.8, color='blue')
    bars3 = ax1.bar(x + width, ach_fine, width, label='ACC=3 (细)', alpha=0.8, color='green')
    
    ax1.set_xlabel('Group ID')
    ax1.set_ylabel('ACH (h⁻¹)')
    ax1.set_title('(a) 各Group在不同网格精度下的ACH值对比')
    ax1.set_xticks(x)
    ax1.set_xticklabels(groups, rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 第二个子图：网格独立性分析
    # 计算中→细的变化率
    change_2_3 = []
    for i in range(len(groups)):
        change = abs(ach_fine[i] - ach_medium[i]) / ach_medium[i] * 100 if ach_medium[i] != 0 else 0
        change_2_3.append(change)
    
    # 根据网格独立性着色
    colors = []
    for change in change_2_3:
        if change < 5:
            colors.append('green')  # 网格独立
        elif change < 10:
            colors.append('orange')  # 边界情况
        else:
            colors.append('red')  # 需要细化
    
    bars = ax2.bar(x, change_2_3, color=colors, alpha=0.7)
    ax2.axhline(y=5, color='red', linestyle='--', alpha=0.8, label='网格独立标准 (5%)')
    ax2.axhline(y=10, color='orange', linestyle='--', alpha=0.8, label='可接受标准 (10%)')
    
    ax2.set_xlabel('Group ID')
    ax2.set_ylabel('中→细 ACH变化率 (%)')
    ax2.set_title('(b) 各Group的网格独立性分析 (中→细变化率)')
    ax2.set_xticks(x)
    ax2.set_xticklabels(groups, rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 添加图例说明
    green_patch = mpatches.Patch(color='green', alpha=0.7, label='网格独立 (<5%)')
    orange_patch = mpatches.Patch(color='orange', alpha=0.7, label='边界情况 (5-10%)')
    red_patch = mpatches.Patch(color='red', alpha=0.7, label='需要细化 (>10%)')
    ax2.legend(handles=[green_patch, orange_patch, red_patch], loc='upper right')
    
    plt.tight_layout()
    plt.savefig('grid_sensitivity_all_groups.png', dpi=300, bbox_inches='tight')
    print(f"📊 所有Group网格敏感性对比图已保存: grid_sensitivity_all_groups.png")
    
    return fig

def create_convergence_trend_plot():
    """创建收敛趋势分析图"""
    
    try:
        df = pd.read_csv('three_mesh_convergence_no_group456.csv')
    except:
        print("无法读取数据进行绘图")
        return
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('网格收敛趋势分析', fontsize=16, fontweight='bold')
    
    # 准备数据
    groups = []
    ach_values = {'coarse': [], 'medium': [], 'fine': []}
    
    for _, row in df.iterrows():
        group_id = int(row['Group'])
        
        ach_1 = row.get('accbuilding_1_no_group456_ACH', np.nan)
        ach_2 = row.get('accbuilding_2_no_group456_ACH', np.nan)
        ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)
        
        if not (np.isnan(ach_1) or np.isnan(ach_2) or np.isnan(ach_3)):
            groups.append(group_id)
            ach_values['coarse'].append(ach_1)
            ach_values['medium'].append(ach_2)
            ach_values['fine'].append(ach_3)
    
    # 选择几个代表性Group进行详细展示
    selected_groups = [0, 7, 20, 29]  # 选择不同收敛特性的Group
    
    for i, group_idx in enumerate(selected_groups):
        if group_idx < len(groups):
            ax = axes[i//2, i%2]
            
            group_id = groups[group_idx]
            mesh_levels = ['粗网格\n(ACC=1)', '中网格\n(ACC=2)', '细网格\n(ACC=3)']
            ach_vals = [ach_values['coarse'][group_idx], 
                       ach_values['medium'][group_idx], 
                       ach_values['fine'][group_idx]]
            
            # 绘制收敛曲线
            ax.plot(mesh_levels, ach_vals, 'o-', linewidth=3, markersize=8, 
                   color='blue', alpha=0.8)
            
            # 计算变化率
            change_1_2 = abs(ach_vals[1] - ach_vals[0]) / ach_vals[0] * 100
            change_2_3 = abs(ach_vals[2] - ach_vals[1]) / ach_vals[1] * 100
            
            # 添加变化率标注
            ax.annotate(f'{change_1_2:.1f}%', 
                       xy=(0.5, (ach_vals[0] + ach_vals[1])/2),
                       ha='center', va='bottom', fontsize=10,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
            
            ax.annotate(f'{change_2_3:.1f}%', 
                       xy=(1.5, (ach_vals[1] + ach_vals[2])/2),
                       ha='center', va='bottom', fontsize=10,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen' if change_2_3 < 5 else 'lightcoral', alpha=0.7))
            
            # 判断收敛状态
            if change_2_3 < 5:
                status = "网格独立 ✅"
                color = 'green'
            elif change_2_3 < 10:
                status = "边界情况 ⚠️"
                color = 'orange'
            else:
                status = "需要细化 ❌"
                color = 'red'
            
            ax.set_title(f'Group {group_id}: {status}', fontsize=12, color=color, fontweight='bold')
            ax.set_ylabel('ACH (h⁻¹)')
            ax.grid(True, alpha=0.3)
            ax.set_ylim(0, max(ach_vals) * 1.2)
    
    plt.tight_layout()
    plt.savefig('grid_convergence_trends.png', dpi=300, bbox_inches='tight')
    print(f"📊 网格收敛趋势分析图已保存: grid_convergence_trends.png")
    
    return fig

def main():
    """主函数"""
    
    print("开始创建网格敏感性分析图表...")
    
    # 创建类似参考图的空间分析图
    create_grid_sensitivity_plot()
    
    # 创建所有Group的对比图
    create_group_comparison_plot()
    
    # 创建收敛趋势分析图
    create_convergence_trend_plot()
    
    print(f"\n{'='*80}")
    print("网格敏感性分析图表创建完成！")
    print("生成的图表:")
    print("• grid_sensitivity_analysis_spatial.png - 空间分布网格敏感性分析")
    print("• grid_sensitivity_all_groups.png - 所有Group网格敏感性对比")
    print("• grid_convergence_trends.png - 网格收敛趋势分析")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
