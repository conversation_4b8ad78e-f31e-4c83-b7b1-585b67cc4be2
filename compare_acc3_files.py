import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def compare_acc3_csv_files():
    """比较ACC=3的两个CSV文件"""
    
    print("="*80)
    print("比较ACC=3的两个CSV文件")
    print("="*80)
    
    # 定义文件路径
    file1_path = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250730_202018.csv"  # 之前使用的
    file2_path = r"D:\XU\2\DATARECORD\GH\wangge\3\ach_groups_20250731_205431.csv"  # 新发现的
    
    print(f"文件1 (之前使用): {file1_path}")
    print(f"文件2 (新发现的): {file2_path}")
    
    try:
        # 读取两个文件
        print(f"\n📂 读取文件...")
        df1 = pd.read_csv(file1_path)
        df2 = pd.read_csv(file2_path)
        
        print(f"✓ 文件1读取成功: {len(df1)} 行数据")
        print(f"✓ 文件2读取成功: {len(df2)} 行数据")
        
    except Exception as e:
        print(f"✗ 文件读取失败: {e}")
        return
    
    # 基本信息比较
    print(f"\n📊 基本信息比较:")
    print(f"{'项目':<20} {'文件1(旧)':<15} {'文件2(新)':<15} {'是否相同':<10}")
    print("-" * 65)
    
    # 行数比较
    rows_same = len(df1) == len(df2)
    print(f"{'行数':<20} {len(df1):<15} {len(df2):<15} {'✅' if rows_same else '❌':<10}")
    
    # 列数比较
    cols_same = len(df1.columns) == len(df2.columns)
    print(f"{'列数':<20} {len(df1.columns):<15} {len(df2.columns):<15} {'✅' if cols_same else '❌':<10}")
    
    # 列名比较
    columns_same = list(df1.columns) == list(df2.columns)
    print(f"{'列名':<20} {'相同' if columns_same else '不同':<15} {'相同' if columns_same else '不同':<15} {'✅' if columns_same else '❌':<10}")
    
    if not columns_same:
        print(f"\n⚠️  列名差异:")
        print(f"  文件1列名: {list(df1.columns)}")
        print(f"  文件2列名: {list(df2.columns)}")
        
        # 找出差异
        cols1_only = set(df1.columns) - set(df2.columns)
        cols2_only = set(df2.columns) - set(df1.columns)
        
        if cols1_only:
            print(f"  仅文件1有: {list(cols1_only)}")
        if cols2_only:
            print(f"  仅文件2有: {list(cols2_only)}")
    
    # 如果基本结构不同，无法进一步比较
    if not (rows_same and cols_same and columns_same):
        print(f"\n❌ 文件结构不同，无法进行详细数据比较")
        return False
    
    # 详细数据比较
    print(f"\n🔍 详细数据比较:")
    
    # 检查是否完全相同
    try:
        files_identical = df1.equals(df2)
        print(f"文件是否完全相同: {'✅ 是' if files_identical else '❌ 否'}")
        
        if files_identical:
            print(f"\n🎉 两个文件完全相同！")
            return True
            
    except Exception as e:
        print(f"比较过程中出错: {e}")
    
    # 如果不完全相同，进行详细分析
    print(f"\n📋 逐列数据比较:")
    
    differences_found = False
    column_differences = {}
    
    for col in df1.columns:
        try:
            if df1[col].dtype in ['float64', 'int64']:
                # 数值列比较
                if df1[col].equals(df2[col]):
                    status = "✅ 相同"
                else:
                    # 检查是否是数值精度问题
                    if np.allclose(df1[col], df2[col], rtol=1e-10, atol=1e-10, equal_nan=True):
                        status = "⚠️  数值精度差异"
                    else:
                        status = "❌ 不同"
                        differences_found = True
                        
                        # 统计差异
                        diff_mask = ~np.isclose(df1[col], df2[col], rtol=1e-10, atol=1e-10, equal_nan=True)
                        diff_count = diff_mask.sum()
                        
                        if diff_count > 0:
                            max_diff = np.abs(df1[col] - df2[col]).max()
                            mean_diff = np.abs(df1[col] - df2[col]).mean()
                            
                            column_differences[col] = {
                                'diff_count': diff_count,
                                'max_diff': max_diff,
                                'mean_diff': mean_diff,
                                'relative_diff': max_diff / np.abs(df1[col]).mean() * 100 if np.abs(df1[col]).mean() > 0 else 0
                            }
                            
                            print(f"  {col:<15}: {status} (差异点数: {diff_count}, 最大差异: {max_diff:.6f}, 平均差异: {mean_diff:.6f})")
                        else:
                            print(f"  {col:<15}: {status}")
            else:
                # 非数值列比较
                if df1[col].equals(df2[col]):
                    status = "✅ 相同"
                else:
                    status = "❌ 不同"
                    differences_found = True
                    
                    # 统计差异
                    diff_count = (df1[col] != df2[col]).sum()
                    print(f"  {col:<15}: {status} (差异点数: {diff_count})")
                    
        except Exception as e:
            print(f"  {col:<15}: ❌ 比较失败 ({e})")
    
    # 如果有差异，进行更详细的分析
    if differences_found:
        print(f"\n🔍 差异详细分析:")
        
        # 按Group分析差异
        if 'GroupID' in df1.columns:
            print(f"\n📊 按Group分析差异:")
            
            group_differences = {}
            
            for group_id in sorted(df1['GroupID'].unique()):
                group1 = df1[df1['GroupID'] == group_id]
                group2 = df2[df2['GroupID'] == group_id]
                
                if len(group1) != len(group2):
                    print(f"  Group {group_id}: 探针数量不同 (文件1: {len(group1)}, 文件2: {len(group2)})")
                    continue
                
                # 检查关键列的差异
                key_columns = ['FlowRate', 'ACH', 'Volume']
                group_has_diff = False
                group_diff_info = {}
                
                for col in key_columns:
                    if col in df1.columns:
                        if not np.allclose(group1[col], group2[col], rtol=1e-10, atol=1e-10, equal_nan=True):
                            if not group_has_diff:
                                group_has_diff = True
                            
                            max_diff = np.abs(group1[col] - group2[col]).max()
                            mean_diff = np.abs(group1[col] - group2[col]).mean()
                            relative_diff = max_diff / np.abs(group1[col]).mean() * 100 if np.abs(group1[col]).mean() > 0 else 0
                            
                            group_diff_info[col] = {
                                'max_diff': max_diff,
                                'mean_diff': mean_diff,
                                'relative_diff': relative_diff
                            }
                
                if group_has_diff:
                    group_differences[group_id] = group_diff_info
                    print(f"  Group {group_id}: 存在差异")
                    for col, diff_info in group_diff_info.items():
                        print(f"    {col}: 最大差异 {diff_info['max_diff']:.6f} ({diff_info['relative_diff']:.2f}%), 平均差异 {diff_info['mean_diff']:.6f}")
        
        # 分析差异的影响
        print(f"\n📈 差异影响分析:")
        
        if 'ACH' in column_differences:
            ach_diff = column_differences['ACH']
            print(f"  ACH值差异:")
            print(f"    最大相对差异: {ach_diff['relative_diff']:.2f}%")
            print(f"    差异点数: {ach_diff['diff_count']} / {len(df1)} ({ach_diff['diff_count']/len(df1)*100:.1f}%)")
            
            if ach_diff['relative_diff'] < 1:
                impact = "轻微"
            elif ach_diff['relative_diff'] < 5:
                impact = "中等"
            else:
                impact = "显著"
            
            print(f"    对网格敏感性分析的影响: {impact}")
        
        # 生成差异统计
        print(f"\n📄 差异统计总结:")
        total_diff_points = sum([info['diff_count'] for info in column_differences.values() if 'diff_count' in info])
        total_points = len(df1) * len([col for col in df1.columns if df1[col].dtype in ['float64', 'int64']])
        
        print(f"  总差异数据点: {total_diff_points}")
        print(f"  总数据点: {total_points}")
        print(f"  差异比例: {total_diff_points/total_points*100:.2f}%")
        
        # 建议
        print(f"\n💡 建议:")
        if any([info['relative_diff'] > 5 for info in column_differences.values() if 'relative_diff' in info]):
            print(f"  ❌ 发现显著差异，建议:")
            print(f"    - 检查两个文件的生成条件")
            print(f"    - 确认计算参数设置")
            print(f"    - 重新进行网格敏感性分析")
        elif any([info['relative_diff'] > 1 for info in column_differences.values() if 'relative_diff' in info]):
            print(f"  ⚠️  发现中等差异，建议:")
            print(f"    - 使用较新的文件进行分析")
            print(f"    - 检查计算收敛性")
        else:
            print(f"  ✅ 差异较小，可以:")
            print(f"    - 使用任一文件进行分析")
            print(f"    - 建议使用较新的文件")
    
    # 总结
    print(f"\n📋 比较总结:")
    if files_identical:
        print(f"✅ 两个文件完全相同")
        return True
    elif not differences_found:
        print(f"✅ 两个文件在数值精度范围内相同")
        return True
    else:
        print(f"❌ 两个文件存在差异")
        
        # 判断差异的严重程度
        if 'ACH' in column_differences:
            max_relative_diff = max([info.get('relative_diff', 0) for info in column_differences.values()])
            if max_relative_diff < 1:
                print(f"差异程度: 轻微 (最大相对差异 < 1%)")
                print(f"建议: 使用较新的文件，对网格敏感性分析影响很小")
            elif max_relative_diff < 5:
                print(f"差异程度: 中等 (最大相对差异 < 5%)")
                print(f"建议: 使用较新的文件，可能需要重新分析")
            else:
                print(f"差异程度: 显著 (最大相对差异 ≥ 5%)")
                print(f"建议: 必须重新进行网格敏感性分析")
        
        return False

def plot_acc3_comparison():
    """绘制ACC=3两个文件的比较图表"""
    
    file1_path = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250730_202018.csv"
    file2_path = r"D:\XU\2\DATARECORD\GH\wangge\3\ach_groups_20250731_205431.csv"
    
    try:
        df1 = pd.read_csv(file1_path)
        df2 = pd.read_csv(file2_path)
    except:
        print("无法读取文件进行绘图")
        return
    
    if len(df1) != len(df2) or list(df1.columns) != list(df2.columns):
        print("文件结构不同，无法绘制比较图")
        return
    
    # 按Group聚合数据
    if 'GroupID' in df1.columns and 'ACH' in df1.columns:
        group1 = df1.groupby('GroupID')['ACH'].first().reset_index()
        group2 = df2.groupby('GroupID')['ACH'].first().reset_index()
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('ACC=3两个文件的详细比较分析', fontsize=16, fontweight='bold')
        
        # 1. ACH值比较
        ax1 = axes[0, 0]
        groups = group1['GroupID']
        ax1.plot(groups, group1['ACH'], 'o-', label='文件1 (202018)', linewidth=2, markersize=6, color='blue')
        ax1.plot(groups, group2['ACH'], 's--', label='文件2 (205431)', linewidth=2, markersize=6, color='red')
        ax1.set_xlabel('Group ID')
        ax1.set_ylabel('ACH (次/小时)')
        ax1.set_title('两个ACC=3文件的ACH值比较')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 差异图
        ax2 = axes[0, 1]
        ach_diff = group2['ACH'] - group1['ACH']
        colors = ['red' if abs(diff) > 0.1 else 'orange' if abs(diff) > 0.01 else 'green' for diff in ach_diff]
        bars = ax2.bar(groups, ach_diff, color=colors, alpha=0.7)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.8)
        ax2.set_xlabel('Group ID')
        ax2.set_ylabel('ACH差异 (文件2 - 文件1)')
        ax2.set_title('ACH值差异分布')
        ax2.grid(True, alpha=0.3)
        
        # 3. 相对差异百分比
        ax3 = axes[1, 0]
        relative_diff = np.abs(ach_diff) / group1['ACH'] * 100
        relative_diff = relative_diff.replace([np.inf, -np.inf], 0)  # 处理除零情况
        
        colors_rel = ['darkred' if diff > 5 else 'red' if diff > 1 else 'orange' if diff > 0.1 else 'green' for diff in relative_diff]
        bars_rel = ax3.bar(groups, relative_diff, color=colors_rel, alpha=0.7)
        ax3.axhline(y=1, color='orange', linestyle='--', alpha=0.8, label='1%线')
        ax3.axhline(y=5, color='red', linestyle='--', alpha=0.8, label='5%线')
        ax3.set_xlabel('Group ID')
        ax3.set_ylabel('相对差异 (%)')
        ax3.set_title('ACH值相对差异百分比')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 散点图对比
        ax4 = axes[1, 1]
        ax4.scatter(group1['ACH'], group2['ACH'], alpha=0.7, s=60)
        
        # 添加对角线
        min_ach = min(min(group1['ACH']), min(group2['ACH']))
        max_ach = max(max(group1['ACH']), max(group2['ACH']))
        ax4.plot([min_ach, max_ach], [min_ach, max_ach], 'r--', alpha=0.8, label='完全相同线')
        
        ax4.set_xlabel('文件1 ACH (次/小时)')
        ax4.set_ylabel('文件2 ACH (次/小时)')
        ax4.set_title('ACH值散点对比')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('acc3_files_comparison.png', dpi=300, bbox_inches='tight')
        print(f"📊 ACC=3比较图表已保存: acc3_files_comparison.png")

def main():
    """主函数"""
    
    print("开始比较ACC=3的两个CSV文件...")
    
    # 比较文件
    files_same = compare_acc3_csv_files()
    
    # 绘制比较图
    plot_acc3_comparison()
    
    print(f"\n{'='*80}")
    if files_same:
        print("结论: 两个ACC=3文件相同或在可接受的数值精度范围内相同")
        print("建议: 可以继续使用当前的网格敏感性分析结果")
    else:
        print("结论: 两个ACC=3文件存在差异")
        print("建议: 根据差异程度决定是否需要重新进行网格敏感性分析")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
