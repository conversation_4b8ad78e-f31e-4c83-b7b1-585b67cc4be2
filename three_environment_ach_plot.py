import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

def create_three_environment_ach_plot():
    """创建三种环境ACH变化图（删除前后建筑数据）"""
    
    print("="*80)
    print("创建三种环境ACH变化图")
    print("删除前后建筑数据，使用黑灰红颜色方案")
    print("="*80)
    
    try:
        # 读取四种环境影响分析结果
        df = pd.read_csv('four_environment_impact_analysis.csv')
        print(f"✓ 成功读取数据: {len(df)} 个Group")
        
        # 提取需要的数据（删除前后建筑数据）
        groups = df['Group'].values
        isolated_ach = df['Isolated_ACH'].values
        front_back_lr_ach = df['Front_Back_LR_ACH'].values  # 前后左右建筑
        front_back_adj_ach = df['Front_Back_Adjusted_ACH'].values  # 调整间距前后建筑
        
        # 过滤有效数据
        valid_mask = ~(np.isnan(isolated_ach) | np.isnan(front_back_lr_ach) | np.isnan(front_back_adj_ach))
        groups_valid = groups[valid_mask]
        isolated_valid = isolated_ach[valid_mask]
        front_back_lr_valid = front_back_lr_ach[valid_mask]
        front_back_adj_valid = front_back_adj_ach[valid_mask]
        
        print(f"有效Group数量: {len(groups_valid)}")
        print(f"Group范围: {groups_valid}")
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建图表
        fig, ax = plt.subplots(1, 1, figsize=(16, 10))
        
        # 使用均匀的X轴位置
        x_pos = range(len(groups_valid))
        
        # 定义黑灰红颜色方案
        colors = {
            'isolated': '#2C2C2C',      # 深灰/黑色
            'front_back_lr': '#808080', # 中灰色
            'front_back_adj': '#DC143C' # 深红色
        }
        
        # 绘制三条线
        ax.plot(x_pos, isolated_valid, 'o-', linewidth=3, markersize=10, 
                color=colors['isolated'], label='孤立建筑', alpha=0.9)
        ax.plot(x_pos, front_back_lr_valid, 's-', linewidth=3, markersize=10, 
                color=colors['front_back_lr'], label='前后左右建筑', alpha=0.9)
        ax.plot(x_pos, front_back_adj_valid, '^-', linewidth=3, markersize=10, 
                color=colors['front_back_adj'], label='调整间距前后建筑', alpha=0.9)
        
        # 设置X轴
        ax.set_xlabel('Group ID', fontsize=16, fontweight='bold')
        ax.set_ylabel('ACH (h⁻¹)', fontsize=16, fontweight='bold')
        ax.set_title('三种环境条件下的ACH变化对比', fontsize=18, fontweight='bold', pad=20)
        
        # 设置X轴刻度和标签
        ax.set_xticks(x_pos)
        ax.set_xticklabels(groups_valid, fontsize=12)
        
        # 设置Y轴
        ax.tick_params(axis='y', labelsize=12)
        
        # 添加图例
        legend = ax.legend(fontsize=14, loc='upper right', frameon=True, 
                          fancybox=True, shadow=True)
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.9)
        
        # 添加网格
        ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.8)
        
        # 设置图表边框
        ax.spines['top'].set_linewidth(1.2)
        ax.spines['right'].set_linewidth(1.2)
        ax.spines['bottom'].set_linewidth(1.2)
        ax.spines['left'].set_linewidth(1.2)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        output_file = 'three_environment_ach_comparison.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        
        print(f"✓ ACH变化图已保存: {output_file}")
        
        # 统计信息
        print(f"\n📊 数据统计:")
        print(f"• 孤立建筑 ACH范围: {isolated_valid.min():.2f} - {isolated_valid.max():.2f}")
        print(f"• 前后左右建筑 ACH范围: {front_back_lr_valid.min():.2f} - {front_back_lr_valid.max():.2f}")
        print(f"• 调整间距前后建筑 ACH范围: {front_back_adj_valid.min():.2f} - {front_back_adj_valid.max():.2f}")
        
        print(f"\n• 孤立建筑 ACH均值: {isolated_valid.mean():.2f}")
        print(f"• 前后左右建筑 ACH均值: {front_back_lr_valid.mean():.2f}")
        print(f"• 调整间距前后建筑 ACH均值: {front_back_adj_valid.mean():.2f}")
        
        # 计算改善情况
        lr_improvement = ((front_back_lr_valid - isolated_valid) / isolated_valid * 100)
        adj_improvement = ((front_back_adj_valid - isolated_valid) / isolated_valid * 100)
        
        lr_positive = (lr_improvement > 0).sum()
        adj_positive = (adj_improvement > 0).sum()
        
        print(f"\n🔍 环境影响分析:")
        print(f"• 前后左右建筑改善Group数: {lr_positive}/{len(groups_valid)} ({lr_positive/len(groups_valid)*100:.1f}%)")
        print(f"• 调整间距前后建筑改善Group数: {adj_positive}/{len(groups_valid)} ({adj_positive/len(groups_valid)*100:.1f}%)")
        print(f"• 前后左右建筑平均改善: {lr_improvement.mean():.1f}%")
        print(f"• 调整间距前后建筑平均改善: {adj_improvement.mean():.1f}%")
        
        return fig
        
    except Exception as e:
        print(f"✗ 创建ACH变化图失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_selected_groups_ach_plot():
    """创建选定Group的ACH变化图（更清晰的展示）"""
    
    print(f"\n{'='*80}")
    print("创建选定Group的ACH变化图")
    print("="*80)
    
    try:
        # 读取数据
        df = pd.read_csv('four_environment_impact_analysis.csv')
        
        # 选择一些代表性的Group（排除Group 13，因为它的数据异常）
        # 选择ACH值相对稳定且有代表性的Group
        selected_groups = [0, 1, 2, 3, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
        
        # 过滤选定的Group
        df_selected = df[df['Group'].isin(selected_groups)].copy()
        df_selected = df_selected.sort_values('Group')
        
        groups = df_selected['Group'].values
        isolated_ach = df_selected['Isolated_ACH'].values
        front_back_lr_ach = df_selected['Front_Back_LR_ACH'].values
        front_back_adj_ach = df_selected['Front_Back_Adjusted_ACH'].values
        
        print(f"选定Group数量: {len(groups)}")
        print(f"选定Group: {groups}")
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建图表
        fig, ax = plt.subplots(1, 1, figsize=(18, 10))
        
        # 使用均匀的X轴位置
        x_pos = range(len(groups))
        
        # 定义黑灰红颜色方案
        colors = {
            'isolated': '#2C2C2C',      # 深灰/黑色
            'front_back_lr': '#808080', # 中灰色
            'front_back_adj': '#DC143C' # 深红色
        }
        
        # 绘制三条线
        ax.plot(x_pos, isolated_ach, 'o-', linewidth=3, markersize=10, 
                color=colors['isolated'], label='孤立建筑', alpha=0.9)
        ax.plot(x_pos, front_back_lr_ach, 's-', linewidth=3, markersize=10, 
                color=colors['front_back_lr'], label='前后左右建筑', alpha=0.9)
        ax.plot(x_pos, front_back_adj_ach, '^-', linewidth=3, markersize=10, 
                color=colors['front_back_adj'], label='调整间距前后建筑', alpha=0.9)
        
        # 设置坐标轴
        ax.set_xlabel('Group ID', fontsize=16, fontweight='bold')
        ax.set_ylabel('ACH (h⁻¹)', fontsize=16, fontweight='bold')
        ax.set_title('选定Group的ACH变化对比（黑灰红配色）', fontsize=18, fontweight='bold', pad=20)
        
        # 设置X轴刻度和标签
        ax.set_xticks(x_pos)
        ax.set_xticklabels(groups, fontsize=12)
        
        # 设置Y轴
        ax.tick_params(axis='y', labelsize=12)
        
        # 添加图例
        legend = ax.legend(fontsize=14, loc='upper right', frameon=True, 
                          fancybox=True, shadow=True)
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.9)
        
        # 添加网格
        ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.8)
        
        # 设置图表边框
        ax.spines['top'].set_linewidth(1.2)
        ax.spines['right'].set_linewidth(1.2)
        ax.spines['bottom'].set_linewidth(1.2)
        ax.spines['left'].set_linewidth(1.2)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        output_file = 'selected_groups_ach_comparison.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        
        print(f"✓ 选定Group ACH变化图已保存: {output_file}")
        
        return fig
        
    except Exception as e:
        print(f"✗ 创建选定Group ACH变化图失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    
    print("开始创建三种环境ACH变化图...")
    
    # 1. 创建包含所有Group的ACH变化图
    create_three_environment_ach_plot()
    
    # 2. 创建选定Group的ACH变化图
    create_selected_groups_ach_plot()
    
    print(f"\n{'='*80}")
    print("ACH变化图创建完成！")
    print("生成的文件:")
    print("• three_environment_ach_comparison.png - 三种环境ACH对比图")
    print("• selected_groups_ach_comparison.png - 选定Group ACH对比图")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
