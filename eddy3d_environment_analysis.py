import pandas as pd
import numpy as np
from scipy import stats
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

def detect_outliers_iqr(data, factor=1.5):
    """使用IQR方法检测异常值"""
    Q1 = np.percentile(data, 25)
    Q3 = np.percentile(data, 75)
    IQR = Q3 - Q1
    lower_bound = Q1 - factor * IQR
    upper_bound = Q3 + factor * IQR
    return (data < lower_bound) | (data > upper_bound)

def detect_outliers_modified_zscore(data, threshold=3.5):
    """使用修正Z-score方法检测异常值"""
    median = np.median(data)
    mad = np.median(np.abs(data - median))
    if mad == 0:
        return np.zeros(len(data), dtype=bool)
    modified_z_scores = 0.6745 * (data - median) / mad
    return np.abs(modified_z_scores) > threshold

def detect_outliers_combined(data):
    """组合方法检测异常值"""
    if len(data) < 4:
        return np.zeros(len(data), dtype=bool)

    outliers_iqr = detect_outliers_iqr(data)
    outliers_zscore = detect_outliers_modified_zscore(data)

    # 使用OR逻辑：任一方法检测到的都认为是异常值
    return outliers_iqr | outliers_zscore

def calculate_robust_ach(group_data, flow_col='FlowRate'):
    """计算稳健的ACH值，包含异常值检测"""

    # 获取流量数据（绝对值）
    flow_rates = np.abs(group_data[flow_col].values)
    volumes = group_data['Volume'].values
    areas = group_data['Area'].values if 'Area' in group_data.columns else np.ones(len(flow_rates))

    # 异常值检测
    outliers = detect_outliers_combined(flow_rates)

    # 清理后的数据
    clean_flow_rates = flow_rates[~outliers]
    clean_volumes = volumes[~outliers]
    clean_areas = areas[~outliers]

    # 统计信息
    total_probes = len(flow_rates)
    outlier_count = np.sum(outliers)
    outlier_rate = outlier_count / total_probes * 100 if total_probes > 0 else 0

    if len(clean_flow_rates) == 0:
        return {
            'Total_Probes': total_probes,
            'Outliers': outlier_count,
            'Outlier_Rate_%': outlier_rate,
            'Clean_Probes': 0,
            'Volume': volumes[0] if len(volumes) > 0 else np.nan,
            'Total_Flow_Rate_m3h': np.nan,
            'Simple_ACH': np.nan,
            'Median_ACH': np.nan,
            'Truncated_Mean_ACH': np.nan,
            'Area_Weighted_ACH': np.nan,
            'Robust_ACH': np.nan
        }

    # 计算总体积
    total_volume = volumes[0] if len(volumes) > 0 else np.nan

    # 计算总流量（m³/s转换为m³/h）
    total_flow_rate = np.sum(clean_flow_rates) * 3600  # m³/h

    # 方法1: 简单ACH（总流量/总体积）
    simple_ach = total_flow_rate / total_volume if total_volume > 0 else np.nan

    # 方法2: 中位数ACH
    individual_ach = (clean_flow_rates * 3600) / clean_volumes
    median_ach = np.median(individual_ach[np.isfinite(individual_ach)])

    # 方法3: 截断均值ACH（去除极值后的均值）
    if len(individual_ach) >= 4:
        sorted_ach = np.sort(individual_ach[np.isfinite(individual_ach)])
        trim_count = max(1, len(sorted_ach) // 10)  # 去除10%的极值
        trimmed_ach = sorted_ach[trim_count:-trim_count] if trim_count < len(sorted_ach)//2 else sorted_ach
        truncated_mean_ach = np.mean(trimmed_ach)
    else:
        truncated_mean_ach = median_ach

    # 方法4: 面积加权ACH
    if len(clean_areas) > 0 and np.sum(clean_areas) > 0:
        weights = clean_areas / np.sum(clean_areas)
        area_weighted_ach = np.sum(individual_ach[np.isfinite(individual_ach)] * weights[:len(individual_ach[np.isfinite(individual_ach)])])
    else:
        area_weighted_ach = median_ach

    # 选择稳健ACH（使用中位数作为最稳健的方法）
    robust_ach = median_ach

    return {
        'Total_Probes': total_probes,
        'Outliers': outlier_count,
        'Outlier_Rate_%': outlier_rate,
        'Clean_Probes': len(clean_flow_rates),
        'Volume': total_volume,
        'Total_Flow_Rate_m3h': total_flow_rate,
        'Simple_ACH': simple_ach,
        'Median_ACH': median_ach,
        'Truncated_Mean_ACH': truncated_mean_ach,
        'Area_Weighted_ACH': area_weighted_ach,
        'Robust_ACH': robust_ach
    }

def load_and_analyze_environment_data():
    """加载并分析三种环境条件的数据，包含异常值检测"""

    print("="*80)
    print("Eddy3D周围环境敏感性分析 - 包含异常值检测")
    print("="*80)

    # 定义文件路径和环境描述
    environments = {
        'isolated': {
            'file': r"D:\XU\2\DATARECORD\GH\huanjing\ach_groups_20250731_150834.csv",
            'name': '孤立建筑',
            'description': '无周围建筑影响的基准情况'
        },
        'front_back': {
            'file': r"D:\XU\2\DATARECORD\GH\huanjing\ach_groups_20250801_221816.csv",
            'name': '前后建筑',
            'description': '风向前后设置相似建筑，间距8m'
        },
        'surrounding': {
            'file': r"D:\XU\2\DATARECORD\GH\huanjing\ach_groups_20250802_115209.csv",
            'name': '四周建筑',
            'description': '前后左右都设置建筑，间距8m'
        }
    }

    # 存储所有环境的数据
    env_data = {}

    for env_key, env_info in environments.items():
        print(f"\n📁 处理 {env_info['name']} 数据...")
        print(f"文件: {env_info['file']}")

        try:
            # 读取数据
            df = pd.read_csv(env_info['file'])
            print(f"✓ 成功读取: {len(df)} 行数据")
            print(f"列名: {list(df.columns)}")

            # 检查数据结构
            if 'GroupID' in df.columns:
                group_col = 'GroupID'
                flow_col = 'FlowRate'
            elif 'Group' in df.columns:
                group_col = 'Group'
                flow_col = 'Flow rate'
            else:
                print(f"⚠️ 未找到Group列，跳过此文件")
                continue

            print(f"Group数量: {df[group_col].nunique()}")
            print(f"Group范围: {df[group_col].min()} - {df[group_col].max()}")

            # 移除Group 4, 5, 6 (与之前保持一致)
            groups_to_remove = [4, 5, 6]
            print(f"移除Group: {groups_to_remove}")

            # 统计被移除的数据
            removed_data = df[df[group_col].isin(groups_to_remove)]
            print(f"移除的探针点数: {len(removed_data)}")

            # 过滤数据
            df_filtered = df[~df[group_col].isin(groups_to_remove)].copy()
            print(f"过滤后数据: {len(df_filtered)} 行")
            print(f"剩余Group数量: {df_filtered[group_col].nunique()}")

            # 计算每个Group的稳健ACH
            group_results = []
            total_outliers = 0
            total_probes = 0

            for group_id in sorted(df_filtered[group_col].unique()):
                group_data = df_filtered[df_filtered[group_col] == group_id]

                # 计算稳健ACH
                result = calculate_robust_ach(group_data, flow_col)
                result['Group'] = group_id

                group_results.append(result)
                total_outliers += result['Outliers']
                total_probes += result['Total_Probes']

            # 创建结果DataFrame
            results_df = pd.DataFrame(group_results)

            # 重新排列列的顺序
            column_order = ['Group', 'Volume', 'Total_Probes', 'Outliers', 'Outlier_Rate_%', 'Clean_Probes',
                          'Total_Flow_Rate_m3h', 'Simple_ACH', 'Median_ACH', 'Truncated_Mean_ACH',
                          'Area_Weighted_ACH', 'Robust_ACH']
            results_df = results_df[column_order]

            # 保存数据
            output_file = f'{env_key}_environment_robust_no_group456.csv'
            results_df.to_csv(output_file, index=False)
            print(f"✓ 数据已保存: {output_file}")

            # 存储到字典
            env_data[env_key] = {
                'data': results_df,
                'info': env_info,
                'file': output_file
            }

            # 统计汇总
            overall_outlier_rate = total_outliers / total_probes * 100 if total_probes > 0 else 0

            print(f"📊 {env_info['name']} 数据汇总:")
            print(f"• 处理的Group数量: {len(results_df)}")
            print(f"• 总探针点数: {total_probes}")
            print(f"• 异常值数量: {total_outliers}")
            print(f"• 异常值比例: {overall_outlier_rate:.1f}%")
            print(f"• 清理后探针点数: {results_df['Clean_Probes'].sum()}")
            print(f"• 总体积: {results_df['Volume'].sum():.1f} m³")
            print(f"• 稳健ACH范围: {results_df['Robust_ACH'].min():.2f} - {results_df['Robust_ACH'].max():.2f}")
            print(f"• 稳健ACH均值: {results_df['Robust_ACH'].mean():.2f}")

        except Exception as e:
            print(f"✗ 处理 {env_info['name']} 失败: {e}")
            import traceback
            traceback.print_exc()
            continue

    return env_data

def create_environment_comparison_analysis(env_data):
    """创建环境比较分析"""
    
    print(f"\n{'='*80}")
    print("环境影响比较分析")
    print("="*80)
    
    if len(env_data) < 2:
        print("⚠️ 需要至少两个环境的数据进行比较")
        return None
    
    # 获取所有环境的数据
    env_names = list(env_data.keys())
    
    # 合并所有环境的数据
    merged_data = None
    
    for env_key, env_info in env_data.items():
        df = env_info['data'][['Group', 'Robust_ACH']].copy()
        df = df.rename(columns={'Robust_ACH': f'{env_key}_ACH'})
        
        if merged_data is None:
            merged_data = df
        else:
            merged_data = merged_data.merge(df, on='Group', how='outer')
    
    # 计算环境影响
    print(f"\n🔍 环境影响分析:")
    print(f"{'Group':<6} {'孤立':<8} {'前后':<8} {'四周':<8} {'前后影响%':<10} {'四周影响%':<10} {'环境敏感性':<12}")
    print("-" * 80)
    
    comparison_results = []
    
    for _, row in merged_data.iterrows():
        group_id = int(row['Group'])
        
        # 获取三种环境的ACH值
        isolated_ach = row.get('isolated_ACH', np.nan)
        front_back_ach = row.get('front_back_ACH', np.nan)
        surrounding_ach = row.get('surrounding_ACH', np.nan)
        
        # 计算环境影响（相对于孤立建筑的变化）
        if not np.isnan(isolated_ach) and isolated_ach != 0:
            if not np.isnan(front_back_ach):
                front_back_impact = (front_back_ach - isolated_ach) / isolated_ach * 100
            else:
                front_back_impact = np.nan
                
            if not np.isnan(surrounding_ach):
                surrounding_impact = (surrounding_ach - isolated_ach) / isolated_ach * 100
            else:
                surrounding_impact = np.nan
        else:
            front_back_impact = np.nan
            surrounding_impact = np.nan
        
        # 判断环境敏感性
        if not (np.isnan(front_back_impact) or np.isnan(surrounding_impact)):
            max_impact = max(abs(front_back_impact), abs(surrounding_impact))
            if max_impact < 5:
                sensitivity = "低敏感"
            elif max_impact < 15:
                sensitivity = "中敏感"
            else:
                sensitivity = "高敏感"
        else:
            sensitivity = "数据缺失"
        
        print(f"{group_id:<6} {isolated_ach:<8.2f} {front_back_ach:<8.2f} {surrounding_ach:<8.2f} "
              f"{front_back_impact:<10.1f} {surrounding_impact:<10.1f} {sensitivity:<12}")
        
        # 添加到结果中
        comparison_results.append({
            'Group': group_id,
            'Isolated_ACH': isolated_ach,
            'Front_Back_ACH': front_back_ach,
            'Surrounding_ACH': surrounding_ach,
            'Front_Back_Impact_%': front_back_impact,
            'Surrounding_Impact_%': surrounding_impact,
            'Environment_Sensitivity': sensitivity
        })
    
    # 创建比较结果DataFrame
    comparison_df = pd.DataFrame(comparison_results)
    
    # 保存比较结果
    output_file = 'environment_impact_analysis.csv'
    comparison_df.to_csv(output_file, index=False)
    print(f"\n✓ 环境影响分析结果已保存: {output_file}")
    
    # 统计汇总
    valid_front_back = comparison_df['Front_Back_Impact_%'].dropna()
    valid_surrounding = comparison_df['Surrounding_Impact_%'].dropna()
    
    if len(valid_front_back) > 0 and len(valid_surrounding) > 0:
        print(f"\n📊 环境影响统计:")
        print(f"• 总Group数: {len(comparison_df)}")
        
        # 前后建筑影响统计
        print(f"\n🏢 前后建筑影响:")
        print(f"• 平均影响: {valid_front_back.mean():.1f}%")
        print(f"• 影响范围: {valid_front_back.min():.1f}% 到 {valid_front_back.max():.1f}%")
        print(f"• 标准差: {valid_front_back.std():.1f}%")
        
        positive_fb = (valid_front_back > 0).sum()
        negative_fb = (valid_front_back < 0).sum()
        print(f"• 正面影响(增加通风): {positive_fb} ({positive_fb/len(valid_front_back)*100:.1f}%)")
        print(f"• 负面影响(减少通风): {negative_fb} ({negative_fb/len(valid_front_back)*100:.1f}%)")
        
        # 四周建筑影响统计
        print(f"\n🏙️ 四周建筑影响:")
        print(f"• 平均影响: {valid_surrounding.mean():.1f}%")
        print(f"• 影响范围: {valid_surrounding.min():.1f}% 到 {valid_surrounding.max():.1f}%")
        print(f"• 标准差: {valid_surrounding.std():.1f}%")
        
        positive_sur = (valid_surrounding > 0).sum()
        negative_sur = (valid_surrounding < 0).sum()
        print(f"• 正面影响(增加通风): {positive_sur} ({positive_sur/len(valid_surrounding)*100:.1f}%)")
        print(f"• 负面影响(减少通风): {negative_sur} ({negative_sur/len(valid_surrounding)*100:.1f}%)")
        
        # 敏感性统计
        sensitivity_counts = comparison_df['Environment_Sensitivity'].value_counts()
        print(f"\n🎯 环境敏感性分布:")
        for sensitivity, count in sensitivity_counts.items():
            if sensitivity != "数据缺失":
                print(f"• {sensitivity}: {count} ({count/len(comparison_df)*100:.1f}%)")
    
    return comparison_df

def create_environment_visualization(env_data, comparison_df):
    """创建环境比较可视化"""
    
    print(f"\n{'='*80}")
    print("创建环境比较可视化")
    print("="*80)
    
    try:
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建多子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
        
        # 1. ACH对比图
        groups = comparison_df['Group'].values
        isolated_ach = comparison_df['Isolated_ACH'].values
        front_back_ach = comparison_df['Front_Back_ACH'].values
        surrounding_ach = comparison_df['Surrounding_ACH'].values
        
        # 过滤有效数据
        valid_mask = ~(np.isnan(isolated_ach) | np.isnan(front_back_ach) | np.isnan(surrounding_ach))
        groups_valid = groups[valid_mask]
        isolated_valid = isolated_ach[valid_mask]
        front_back_valid = front_back_ach[valid_mask]
        surrounding_valid = surrounding_ach[valid_mask]
        
        x_pos = range(len(groups_valid))
        
        # 定义颜色
        colors = {
            'isolated': '#2E8B57',    # 海绿色
            'front_back': '#FF6347',  # 番茄红
            'surrounding': '#4169E1'  # 皇家蓝
        }
        
        ax1.plot(x_pos, isolated_valid, 'o-', linewidth=3, markersize=8, 
                color=colors['isolated'], label='孤立建筑', alpha=0.8)
        ax1.plot(x_pos, front_back_valid, 's--', linewidth=3, markersize=8, 
                color=colors['front_back'], label='前后建筑', alpha=0.8)
        ax1.plot(x_pos, surrounding_valid, '^-', linewidth=3, markersize=8, 
                color=colors['surrounding'], label='四周建筑', alpha=0.8)
        
        ax1.set_xlabel('Group ID', fontsize=14, fontweight='bold')
        ax1.set_ylabel('ACH (h⁻¹)', fontsize=14, fontweight='bold')
        ax1.set_title('不同环境条件下的ACH对比', fontsize=16, fontweight='bold')
        ax1.set_xticks(x_pos)
        ax1.set_xticklabels(groups_valid, fontsize=12)
        ax1.legend(fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # 2. 环境影响百分比图
        front_back_impact = comparison_df['Front_Back_Impact_%'].dropna()
        surrounding_impact = comparison_df['Surrounding_Impact_%'].dropna()
        
        ax2.scatter(front_back_impact, surrounding_impact, s=100, alpha=0.7, 
                   c='#FF6347', edgecolors='black', linewidth=1)
        
        # 添加参考线
        ax2.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
        ax2.axvline(x=0, color='gray', linestyle='--', alpha=0.5)
        
        ax2.set_xlabel('前后建筑影响 (%)', fontsize=14, fontweight='bold')
        ax2.set_ylabel('四周建筑影响 (%)', fontsize=14, fontweight='bold')
        ax2.set_title('环境影响相关性分析', fontsize=16, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 3. 环境影响分布直方图
        ax3.hist([front_back_impact, surrounding_impact], 
                bins=15, alpha=0.7, label=['前后建筑', '四周建筑'],
                color=[colors['front_back'], colors['surrounding']])
        
        ax3.set_xlabel('ACH变化 (%)', fontsize=14, fontweight='bold')
        ax3.set_ylabel('Group数量', fontsize=14, fontweight='bold')
        ax3.set_title('环境影响分布', fontsize=16, fontweight='bold')
        ax3.legend(fontsize=12)
        ax3.grid(True, alpha=0.3)
        
        # 4. 敏感性分析饼图
        sensitivity_counts = comparison_df['Environment_Sensitivity'].value_counts()
        # 移除"数据缺失"
        if "数据缺失" in sensitivity_counts.index:
            sensitivity_counts = sensitivity_counts.drop("数据缺失")
        
        colors_pie = ['#90EE90', '#FFD700', '#FF6347']  # 浅绿、金色、番茄红
        
        wedges, texts, autotexts = ax4.pie(sensitivity_counts.values, 
                                          labels=sensitivity_counts.index,
                                          autopct='%1.1f%%',
                                          colors=colors_pie,
                                          startangle=90)
        
        ax4.set_title('环境敏感性分布', fontsize=16, fontweight='bold')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        plt.savefig('environment_impact_analysis.png', dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"📊 环境影响分析图已保存: environment_impact_analysis.png")
        
        return fig
        
    except Exception as e:
        print(f"✗ 创建可视化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    
    print("开始Eddy3D周围环境敏感性分析...")
    
    # 1. 加载和分析环境数据
    env_data = load_and_analyze_environment_data()
    
    if len(env_data) >= 2:
        # 2. 创建环境比较分析
        comparison_df = create_environment_comparison_analysis(env_data)
        
        if comparison_df is not None:
            # 3. 创建可视化
            create_environment_visualization(env_data, comparison_df)
            
            print(f"\n{'='*80}")
            print("Eddy3D环境敏感性分析完成！")
            print("生成的文件:")
            print("• isolated_environment_no_group456.csv - 孤立建筑数据")
            print("• front_back_environment_no_group456.csv - 前后建筑数据")
            print("• surrounding_environment_no_group456.csv - 四周建筑数据")
            print("• environment_impact_analysis.csv - 环境影响分析结果")
            print("• environment_impact_analysis.png - 环境影响分析图")
            print(f"{'='*80}")
        else:
            print("环境比较分析失败")
    else:
        print("环境数据加载失败，无法进行比较分析")

if __name__ == "__main__":
    main()
