import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
import seaborn as sns

def analyze_single_mesh_detailed(csv_path, mesh_name="accbuilding_3"):
    """详细分析单个网格的ACH数据"""
    
    print(f"{'='*80}")
    print(f"详细分析网格: {mesh_name}")
    print(f"数据文件: {csv_path}")
    print(f"{'='*80}")
    
    # 读取数据
    try:
        df = pd.read_csv(csv_path)
        print(f"✓ 成功读取数据，共 {len(df)} 个探针点")
    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return None
    
    # 数据概览
    print(f"\n📊 数据概览:")
    print(f"  总探针数: {len(df)}")
    print(f"  Group数量: {df['GroupID'].nunique()}")
    print(f"  数据列: {list(df.columns)}")
    
    # 按Group分组分析
    grouped = df.groupby('GroupID')
    results = []
    
    print(f"\n🏢 各Group详细分析:")
    print(f"{'Group':<6} {'探针数':<8} {'体积(m³)':<10} {'ACH':<12} {'状态':<15} {'备注'}")
    print("-" * 80)
    
    for group_id, group_data in grouped:
        # 基本信息
        probe_count = len(group_data)
        volume = group_data['Volume'].iloc[0]
        flow_rates = group_data['FlowRate'].values
        areas = group_data['Area'].values if 'Area' in group_data.columns else np.ones(len(flow_rates))
        
        # 异常值检测
        abs_flow_rates = np.abs(flow_rates)
        Q1 = np.percentile(abs_flow_rates, 25)
        Q3 = np.percentile(abs_flow_rates, 75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = (abs_flow_rates < lower_bound) | (abs_flow_rates > upper_bound)
        outlier_count = np.sum(outliers)
        
        # 清理数据
        if outlier_count > 0 and len(flow_rates) - outlier_count >= 1:
            clean_flow_rates = flow_rates[~outliers]
            valid_probes = len(clean_flow_rates)
        else:
            clean_flow_rates = flow_rates
            valid_probes = len(clean_flow_rates)
        
        # 计算ACH (使用中位数方法)
        Q_median = np.median(np.abs(clean_flow_rates)) * 3600  # m³/h
        ach = Q_median / volume if volume != 0 else 0
        
        # 状态判断
        if ach < 0.5:
            status = "通风不足"
            note = "ACH过低"
        elif ach > 50:
            status = "数值异常"
            note = "ACH过高，需检查"
        elif outlier_count > probe_count * 0.2:  # 超过20%异常值
            status = "数据质量差"
            note = f"异常值多({outlier_count}个)"
        elif valid_probes < 10:
            status = "探针不足"
            note = "建议增加探针"
        else:
            status = "正常"
            note = ""
        
        # 输出结果
        print(f"{group_id:<6} {probe_count:<8} {volume:<10.3f} {ach:<12.6f} {status:<15} {note}")
        
        # 存储结果
        results.append({
            'GroupID': group_id,
            'Volume': volume,
            'Total_Probes': probe_count,
            'Valid_Probes': valid_probes,
            'Outliers_Removed': outlier_count,
            'ACH': ach,
            'Status': status,
            'Flow_Rate_Min': np.min(clean_flow_rates),
            'Flow_Rate_Max': np.max(clean_flow_rates),
            'Flow_Rate_Median': np.median(clean_flow_rates),
            'Flow_Rate_Std': np.std(clean_flow_rates)
        })
    
    # 转换为DataFrame
    results_df = pd.DataFrame(results)
    
    # 统计分析
    print(f"\n📈 统计分析:")
    print(f"  ACH范围: {results_df['ACH'].min():.6f} - {results_df['ACH'].max():.6f}")
    print(f"  ACH平均值: {results_df['ACH'].mean():.6f}")
    print(f"  ACH中位数: {results_df['ACH'].median():.6f}")
    print(f"  ACH标准差: {results_df['ACH'].std():.6f}")
    
    # 问题诊断
    print(f"\n🔍 问题诊断:")
    low_ach = results_df[results_df['ACH'] < 0.5]
    high_ach = results_df[results_df['ACH'] > 50]
    low_probes = results_df[results_df['Valid_Probes'] < 10]
    high_outliers = results_df[results_df['Outliers_Removed'] > results_df['Total_Probes'] * 0.2]
    
    if len(low_ach) > 0:
        print(f"  ⚠️  通风不足的Group: {list(low_ach['GroupID'])}")
    if len(high_ach) > 0:
        print(f"  ⚠️  ACH异常高的Group: {list(high_ach['GroupID'])}")
    if len(low_probes) > 0:
        print(f"  ⚠️  探针不足的Group: {list(low_probes['GroupID'])}")
    if len(high_outliers) > 0:
        print(f"  ⚠️  异常值较多的Group: {list(high_outliers['GroupID'])}")
    
    if len(low_ach) == 0 and len(high_ach) == 0 and len(low_probes) == 0 and len(high_outliers) == 0:
        print(f"  ✓ 所有Group数据质量良好")
    
    # 保存详细结果
    output_file = f"{mesh_name}_detailed_analysis.csv"
    results_df.to_csv(output_file, index=False)
    print(f"\n💾 详细结果已保存到: {output_file}")
    
    # 网格敏感性分析建议
    print(f"\n🎯 网格敏感性分析建议:")
    print(f"  1. 当前网格 ({mesh_name}) 基线ACH值已确定")
    print(f"  2. 建议测试更精细网格 (如 accbuilding_4, accbuilding_5)")
    print(f"  3. 关注ACH变化较大的Group: {list(results_df.nlargest(3, 'ACH')['GroupID'])}")
    print(f"  4. 收敛判断标准: 相邻网格ACH变化 < 5%")
    
    # 推荐下一步网格设置
    avg_ach = results_df['ACH'].mean()
    print(f"\n📋 下一步网格测试计划:")
    print(f"  - 当前平均ACH: {avg_ach:.6f}")
    print(f"  - 建议精细网格: accbuilding_4 (预期ACH变化 < 10%)")
    print(f"  - 建议超精细网格: accbuilding_5 (用于确认收敛)")
    print(f"  - 重点监控Group: {', '.join(map(str, results_df.nlargest(5, 'ACH')['GroupID']))}")
    
    return results_df

def create_visualization(results_df, mesh_name):
    """创建可视化图表"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle(f'{mesh_name} 网格 ACH 分析', fontsize=16, fontweight='bold')
    
    # 1. ACH分布直方图
    axes[0,0].hist(results_df['ACH'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0,0].set_xlabel('ACH')
    axes[0,0].set_ylabel('频数')
    axes[0,0].set_title('ACH分布直方图')
    axes[0,0].grid(True, alpha=0.3)
    
    # 2. 各Group的ACH值
    axes[0,1].bar(results_df['GroupID'], results_df['ACH'], alpha=0.7, color='lightcoral')
    axes[0,1].set_xlabel('Group ID')
    axes[0,1].set_ylabel('ACH')
    axes[0,1].set_title('各Group ACH值')
    axes[0,1].tick_params(axis='x', rotation=45)
    axes[0,1].grid(True, alpha=0.3)
    
    # 3. 探针数量 vs ACH
    scatter = axes[1,0].scatter(results_df['Valid_Probes'], results_df['ACH'], 
                               c=results_df['Volume'], cmap='viridis', alpha=0.7)
    axes[1,0].set_xlabel('有效探针数')
    axes[1,0].set_ylabel('ACH')
    axes[1,0].set_title('探针数量 vs ACH (颜色=体积)')
    plt.colorbar(scatter, ax=axes[1,0], label='体积 (m³)')
    axes[1,0].grid(True, alpha=0.3)
    
    # 4. 体积 vs ACH
    axes[1,1].scatter(results_df['Volume'], results_df['ACH'], alpha=0.7, color='orange')
    axes[1,1].set_xlabel('体积 (m³)')
    axes[1,1].set_ylabel('ACH')
    axes[1,1].set_title('体积 vs ACH')
    axes[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    plot_file = f"{mesh_name}_analysis_plots.png"
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"📊 分析图表已保存到: {plot_file}")
    
    plt.show()

def main():
    """主函数"""
    # 分析您的数据
    csv_path = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250730_202018.csv"
    mesh_name = "accbuilding_3"
    
    # 执行详细分析
    results_df = analyze_single_mesh_detailed(csv_path, mesh_name)
    
    if results_df is not None:
        # 创建可视化
        create_visualization(results_df, mesh_name)
        
        print(f"\n{'='*80}")
        print(f"分析完成！生成的文件:")
        print(f"  - {mesh_name}_detailed_analysis.csv: 详细分析结果")
        print(f"  - {mesh_name}_analysis_plots.png: 可视化图表")
        print(f"{'='*80}")

if __name__ == "__main__":
    main()
