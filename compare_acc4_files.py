import pandas as pd
import numpy as np

def compare_acc4_files():
    """比较两个ACC=4 CSV文件"""
    
    print("="*80)
    print("比较两个ACC=4 CSV文件")
    print("="*80)
    
    # 文件路径
    file1 = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250731_232707.csv"
    file2 = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250731_233520.csv"
    
    print(f"文件1: {file1}")
    print(f"文件2: {file2}")
    
    try:
        # 读取两个文件
        df1 = pd.read_csv(file1)
        df2 = pd.read_csv(file2)
        
        print(f"\n✓ 成功读取两个文件")
        print(f"文件1形状: {df1.shape}")
        print(f"文件2形状: {df2.shape}")
        
        # 检查基本信息
        print(f"\n📊 基本信息对比:")
        print(f"{'项目':<20} {'文件1':<15} {'文件2':<15} {'是否相同':<10}")
        print("-" * 65)
        print(f"{'行数':<20} {len(df1):<15} {len(df2):<15} {len(df1) == len(df2)}")
        print(f"{'列数':<20} {len(df1.columns):<15} {len(df2.columns):<15} {len(df1.columns) == len(df2.columns)}")
        
        # 检查列名
        cols_same = list(df1.columns) == list(df2.columns)
        print(f"{'列名':<20} {'-':<15} {'-':<15} {cols_same}")
        
        if not cols_same:
            print(f"\n⚠️ 列名不同:")
            print(f"文件1列名: {list(df1.columns)}")
            print(f"文件2列名: {list(df2.columns)}")
            return
        
        # 检查Group信息
        groups1 = set(df1['GroupID'].unique())
        groups2 = set(df2['GroupID'].unique())
        groups_same = groups1 == groups2
        
        print(f"{'Group数量':<20} {len(groups1):<15} {len(groups2):<15} {len(groups1) == len(groups2)}")
        print(f"{'Group内容':<20} {'-':<15} {'-':<15} {groups_same}")
        
        if not groups_same:
            print(f"\n⚠️ Group不同:")
            print(f"文件1独有: {groups1 - groups2}")
            print(f"文件2独有: {groups2 - groups1}")
        
        # 检查数据是否完全相同
        if df1.shape == df2.shape and cols_same:
            # 按GroupID和PointID排序以确保比较顺序一致
            df1_sorted = df1.sort_values(['GroupID', 'PointID']).reset_index(drop=True)
            df2_sorted = df2.sort_values(['GroupID', 'PointID']).reset_index(drop=True)
            
            # 比较所有数值列
            numerical_cols = ['GroupID', 'PointID', 'FlowRate', 'X', 'Y', 'Z', 'Area', 'Volume', 'ACH']
            
            print(f"\n🔍 详细数值比较:")
            print(f"{'列名':<15} {'完全相同':<10} {'最大差异':<15} {'平均差异':<15}")
            print("-" * 60)
            
            all_same = True
            
            for col in numerical_cols:
                if col in df1_sorted.columns and col in df2_sorted.columns:
                    values1 = df1_sorted[col].values
                    values2 = df2_sorted[col].values
                    
                    # 检查是否完全相同
                    if col in ['GroupID', 'PointID']:
                        # 整数列
                        is_same = np.array_equal(values1, values2)
                        max_diff = np.max(np.abs(values1 - values2)) if not is_same else 0
                        mean_diff = np.mean(np.abs(values1 - values2)) if not is_same else 0
                    else:
                        # 浮点数列，考虑精度误差
                        diff = np.abs(values1 - values2)
                        is_same = np.allclose(values1, values2, rtol=1e-10, atol=1e-10)
                        max_diff = np.max(diff)
                        mean_diff = np.mean(diff)
                    
                    print(f"{col:<15} {is_same:<10} {max_diff:<15.2e} {mean_diff:<15.2e}")
                    
                    if not is_same:
                        all_same = False
            
            # 检查FlowDir列（字符串）
            if 'FlowDir' in df1_sorted.columns and 'FlowDir' in df2_sorted.columns:
                flowdir_same = (df1_sorted['FlowDir'] == df2_sorted['FlowDir']).all()
                print(f"{'FlowDir':<15} {flowdir_same:<10} {'-':<15} {'-':<15}")
                if not flowdir_same:
                    all_same = False
            
            print(f"\n📋 总体结论:")
            if all_same:
                print("✅ 两个文件完全相同！")
            else:
                print("❌ 两个文件存在差异")
                
                # 显示差异最大的几行
                print(f"\n🔍 差异分析:")
                for col in ['FlowRate', 'ACH']:
                    if col in df1_sorted.columns:
                        diff = np.abs(df1_sorted[col] - df2_sorted[col])
                        max_diff_idx = np.argmax(diff)
                        max_diff_val = diff.iloc[max_diff_idx]
                        
                        if max_diff_val > 1e-10:
                            print(f"\n{col}最大差异:")
                            print(f"位置: 行{max_diff_idx}, GroupID={df1_sorted.iloc[max_diff_idx]['GroupID']}, PointID={df1_sorted.iloc[max_diff_idx]['PointID']}")
                            print(f"文件1值: {df1_sorted.iloc[max_diff_idx][col]}")
                            print(f"文件2值: {df2_sorted.iloc[max_diff_idx][col]}")
                            print(f"差异: {max_diff_val}")
        
        # 按Group统计比较
        print(f"\n📊 按Group统计比较:")
        print(f"{'Group':<6} {'文件1-ACH均值':<15} {'文件2-ACH均值':<15} {'差异':<10} {'相对差异%':<12}")
        print("-" * 70)
        
        for group_id in sorted(groups1.intersection(groups2)):
            group1_data = df1[df1['GroupID'] == group_id]
            group2_data = df2[df2['GroupID'] == group_id]
            
            if len(group1_data) > 0 and len(group2_data) > 0:
                ach1_mean = group1_data['ACH'].mean()
                ach2_mean = group2_data['ACH'].mean()
                diff = abs(ach1_mean - ach2_mean)
                rel_diff = diff / ach1_mean * 100 if ach1_mean != 0 else 0
                
                print(f"{group_id:<6} {ach1_mean:<15.6f} {ach2_mean:<15.6f} {diff:<10.6f} {rel_diff:<12.3f}")
        
        return df1, df2
        
    except Exception as e:
        print(f"✗ 比较文件失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """主函数"""
    
    print("开始比较两个ACC=4 CSV文件...")
    
    df1, df2 = compare_acc4_files()
    
    if df1 is not None and df2 is not None:
        print(f"\n{'='*80}")
        print("文件比较完成！")
        print("如果文件相同，可以使用任意一个进行分析")
        print("如果文件不同，需要确认使用哪个版本")
        print(f"{'='*80}")
    else:
        print("文件比较失败")

if __name__ == "__main__":
    main()
