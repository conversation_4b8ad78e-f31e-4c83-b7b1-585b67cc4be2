import pandas as pd
import numpy as np
from scipy import stats

def detect_outliers_combined(data, iqr_factor=1.5, z_threshold=3):
    """组合异常值检测方法 - 与网格敏感性分析相同"""
    if len(data) == 0:
        return np.array([], dtype=bool)
    
    data_array = np.array(data)
    outliers = np.zeros(len(data_array), dtype=bool)
    
    # IQR方法
    Q1 = np.percentile(data_array, 25)
    Q3 = np.percentile(data_array, 75)
    IQR = Q3 - Q1
    lower_bound = Q1 - iqr_factor * IQR
    upper_bound = Q3 + iqr_factor * IQR
    iqr_outliers = (data_array < lower_bound) | (data_array > upper_bound)
    
    # Modified Z-score方法
    median = np.median(data_array)
    mad = np.median(np.abs(data_array - median))
    if mad != 0:
        modified_z_scores = 0.6745 * (data_array - median) / mad
        z_outliers = np.abs(modified_z_scores) > z_threshold
    else:
        z_outliers = np.zeros(len(data_array), dtype=bool)
    
    # 组合判断：两种方法都认为是异常值才标记
    outliers = iqr_outliers & z_outliers
    
    return outliers

def calculate_robust_ach(group_data, flow_col='FlowRate'):
    """计算稳健的ACH值 - 与网格敏感性分析相同"""
    if len(group_data) == 0:
        return np.nan, np.nan, 0, 0
    
    # 获取流量数据（绝对值）
    flow_rates = np.abs(group_data[flow_col].values)
    volumes = group_data['Volume'].values
    
    if len(flow_rates) == 0 or np.all(volumes == 0):
        return np.nan, np.nan, 0, 0
    
    # 异常值检测
    outliers = detect_outliers_combined(flow_rates)
    
    # 清理后的数据
    clean_flow_rates = flow_rates[~outliers]
    clean_volumes = volumes[~outliers]
    
    if len(clean_flow_rates) == 0:
        return np.nan, np.nan, len(outliers), np.sum(outliers)
    
    # 计算总流量（m³/s转换为m³/h）
    total_flow_rate = np.sum(clean_flow_rates) * 3600  # m³/h
    
    # 计算总体积
    total_volume = clean_volumes[0] if len(clean_volumes) > 0 else np.nan
    
    # 计算ACH
    if total_volume > 0:
        ach = total_flow_rate / total_volume
    else:
        ach = np.nan
    
    # 使用中位数方法作为稳健估计
    if len(clean_flow_rates) > 0:
        individual_ach = (clean_flow_rates * 3600) / clean_volumes
        robust_ach = np.median(individual_ach[np.isfinite(individual_ach)])
    else:
        robust_ach = np.nan
    
    return ach, robust_ach, len(flow_rates), np.sum(outliers)

def process_environment_file(file_path, env_name, output_suffix):
    """处理单个环境文件"""
    
    print(f"\n{'='*60}")
    print(f"处理 {env_name} 数据")
    print(f"{'='*60}")
    
    try:
        # 读取数据
        df = pd.read_csv(file_path)
        print(f"✓ 成功读取文件: {file_path}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 检查数据结构
        if 'GroupID' in df.columns:
            group_col = 'GroupID'
            flow_col = 'FlowRate'
        elif 'Group' in df.columns:
            group_col = 'Group'
            flow_col = 'Flow rate'
        else:
            print(f"⚠️ 未找到Group列")
            return None
        
        print(f"\n原始数据统计:")
        print(f"总行数: {len(df)}")
        print(f"Group数量: {df[group_col].nunique()}")
        print(f"Group范围: {df[group_col].min()} - {df[group_col].max()}")
        
        # 移除Group 4, 5, 6 (与网格敏感性分析保持一致)
        groups_to_remove = [4, 5, 6]
        print(f"\n移除Group: {groups_to_remove}")
        
        # 统计被移除的数据
        removed_data = df[df[group_col].isin(groups_to_remove)]
        print(f"移除的探针点数: {len(removed_data)}")
        if len(removed_data) > 0:
            removed_volume = removed_data.groupby(group_col)['Volume'].first().sum()
            print(f"移除的总体积: {removed_volume:.1f} m³")
        
        # 过滤数据
        df_filtered = df[~df[group_col].isin(groups_to_remove)].copy()
        print(f"过滤后数据: {len(df_filtered)} 行")
        print(f"剩余Group数量: {df_filtered[group_col].nunique()}")
        
        # 按Group计算稳健ACH
        results = []
        
        print(f"\n开始计算各Group的稳健ACH值...")
        print(f"{'Group':<6} {'探针数':<8} {'异常值':<8} {'体积(m³)':<10} {'ACH':<10} {'稳健ACH':<10}")
        print("-" * 60)
        
        for group_id in sorted(df_filtered[group_col].unique()):
            group_data = df_filtered[df_filtered[group_col] == group_id]
            
            # 计算稳健ACH
            ach, robust_ach, total_probes, outliers_count = calculate_robust_ach(group_data, flow_col)
            
            # 获取体积
            volume = group_data['Volume'].iloc[0] if len(group_data) > 0 else np.nan
            
            print(f"{group_id:<6} {total_probes:<8} {outliers_count:<8} {volume:<10.1f} {ach:<10.2f} {robust_ach:<10.2f}")
            
            results.append({
                'Group': group_id,
                'Volume': volume,
                'Total_Probes': total_probes,
                'Outliers_Count': outliers_count,
                'ACH': ach,
                'Robust_ACH': robust_ach,
                'Outlier_Rate': outliers_count / total_probes * 100 if total_probes > 0 else 0
            })
        
        # 创建结果DataFrame
        results_df = pd.DataFrame(results)
        
        # 保存清理后的数据
        output_file = f'{output_suffix}_robust_no_group456.csv'
        results_df.to_csv(output_file, index=False)
        print(f"\n✓ {env_name}稳健处理数据已保存: {output_file}")
        
        # 统计汇总
        print(f"\n📊 {env_name}数据处理汇总:")
        print(f"• 处理的Group数量: {len(results_df)}")
        print(f"• 总探针点数: {results_df['Total_Probes'].sum()}")
        print(f"• 总异常值数: {results_df['Outliers_Count'].sum()}")
        print(f"• 平均异常值率: {results_df['Outlier_Rate'].mean():.1f}%")
        print(f"• 总体积: {results_df['Volume'].sum():.1f} m³")
        print(f"• 稳健ACH范围: {results_df['Robust_ACH'].min():.2f} - {results_df['Robust_ACH'].max():.2f}")
        print(f"• 稳健ACH均值: {results_df['Robust_ACH'].mean():.2f}")
        
        # 检查是否有无效的ACH值
        invalid_ach = results_df['Robust_ACH'].isna().sum()
        if invalid_ach > 0:
            print(f"⚠️ 警告: {invalid_ach} 个Group的ACH值无效")
            invalid_groups = results_df[results_df['Robust_ACH'].isna()]['Group'].tolist()
            print(f"无效ACH的Group: {invalid_groups}")
        
        return results_df
        
    except Exception as e:
        print(f"✗ 处理 {env_name} 失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_robust_environment_comparison():
    """创建稳健环境比较分析"""
    
    print(f"\n{'='*80}")
    print("稳健环境影响比较分析")
    print("="*80)
    
    try:
        # 读取三个稳健处理后的数据文件
        df_isolated = pd.read_csv('isolated_robust_no_group456.csv')
        df_front_back = pd.read_csv('front_back_robust_no_group456.csv')
        df_surrounding = pd.read_csv('surrounding_robust_no_group456.csv')
        
        print(f"✓ 读取稳健处理数据:")
        print(f"• 孤立建筑: {len(df_isolated)} 个Group")
        print(f"• 前后建筑: {len(df_front_back)} 个Group")
        print(f"• 四周建筑: {len(df_surrounding)} 个Group")
        
        # 合并数据
        merged_df = df_isolated[['Group', 'Robust_ACH']].copy()
        merged_df = merged_df.rename(columns={'Robust_ACH': 'Isolated_Robust_ACH'})
        
        merged_df = merged_df.merge(df_front_back[['Group', 'Robust_ACH']], 
                                   on='Group', how='outer')
        merged_df = merged_df.rename(columns={'Robust_ACH': 'Front_Back_Robust_ACH'})
        
        merged_df = merged_df.merge(df_surrounding[['Group', 'Robust_ACH']], 
                                   on='Group', how='outer')
        merged_df = merged_df.rename(columns={'Robust_ACH': 'Surrounding_Robust_ACH'})
        
        print(f"✓ 成功合并数据: {len(merged_df)} 个Group")
        
        # 计算环境影响
        print(f"\n🔍 稳健环境影响分析:")
        print(f"{'Group':<6} {'孤立':<8} {'前后':<8} {'四周':<8} {'前后影响%':<10} {'四周影响%':<10} {'环境敏感性':<12}")
        print("-" * 80)
        
        comparison_results = []
        
        for _, row in merged_df.iterrows():
            group_id = int(row['Group'])
            
            # 获取三种环境的稳健ACH值
            isolated_ach = row.get('Isolated_Robust_ACH', np.nan)
            front_back_ach = row.get('Front_Back_Robust_ACH', np.nan)
            surrounding_ach = row.get('Surrounding_Robust_ACH', np.nan)
            
            # 计算环境影响（相对于孤立建筑的变化）
            if not np.isnan(isolated_ach) and isolated_ach != 0:
                if not np.isnan(front_back_ach):
                    front_back_impact = (front_back_ach - isolated_ach) / isolated_ach * 100
                else:
                    front_back_impact = np.nan
                    
                if not np.isnan(surrounding_ach):
                    surrounding_impact = (surrounding_ach - isolated_ach) / isolated_ach * 100
                else:
                    surrounding_impact = np.nan
            else:
                front_back_impact = np.nan
                surrounding_impact = np.nan
            
            # 判断环境敏感性
            if not (np.isnan(front_back_impact) or np.isnan(surrounding_impact)):
                max_impact = max(abs(front_back_impact), abs(surrounding_impact))
                if max_impact < 5:
                    sensitivity = "低敏感"
                elif max_impact < 15:
                    sensitivity = "中敏感"
                else:
                    sensitivity = "高敏感"
            else:
                sensitivity = "数据缺失"
            
            print(f"{group_id:<6} {isolated_ach:<8.2f} {front_back_ach:<8.2f} {surrounding_ach:<8.2f} "
                  f"{front_back_impact:<10.1f} {surrounding_impact:<10.1f} {sensitivity:<12}")
            
            # 添加到结果中
            comparison_results.append({
                'Group': group_id,
                'Isolated_Robust_ACH': isolated_ach,
                'Front_Back_Robust_ACH': front_back_ach,
                'Surrounding_Robust_ACH': surrounding_ach,
                'Front_Back_Impact_%': front_back_impact,
                'Surrounding_Impact_%': surrounding_impact,
                'Environment_Sensitivity': sensitivity
            })
        
        # 创建比较结果DataFrame
        comparison_df = pd.DataFrame(comparison_results)
        
        # 保存比较结果
        output_file = 'robust_environment_impact_analysis.csv'
        comparison_df.to_csv(output_file, index=False)
        print(f"\n✓ 稳健环境影响分析结果已保存: {output_file}")
        
        # 统计汇总
        valid_front_back = comparison_df['Front_Back_Impact_%'].dropna()
        valid_surrounding = comparison_df['Surrounding_Impact_%'].dropna()
        
        if len(valid_front_back) > 0 and len(valid_surrounding) > 0:
            print(f"\n📊 稳健环境影响统计:")
            print(f"• 总Group数: {len(comparison_df)}")
            
            # 前后建筑影响统计
            print(f"\n🏢 前后建筑影响 (稳健分析):")
            print(f"• 平均影响: {valid_front_back.mean():.1f}%")
            print(f"• 影响范围: {valid_front_back.min():.1f}% 到 {valid_front_back.max():.1f}%")
            print(f"• 标准差: {valid_front_back.std():.1f}%")
            
            positive_fb = (valid_front_back > 0).sum()
            negative_fb = (valid_front_back < 0).sum()
            print(f"• 正面影响(增加通风): {positive_fb} ({positive_fb/len(valid_front_back)*100:.1f}%)")
            print(f"• 负面影响(减少通风): {negative_fb} ({negative_fb/len(valid_front_back)*100:.1f}%)")
            
            # 四周建筑影响统计
            print(f"\n🏙️ 四周建筑影响 (稳健分析):")
            print(f"• 平均影响: {valid_surrounding.mean():.1f}%")
            print(f"• 影响范围: {valid_surrounding.min():.1f}% 到 {valid_surrounding.max():.1f}%")
            print(f"• 标准差: {valid_surrounding.std():.1f}%")
            
            positive_sur = (valid_surrounding > 0).sum()
            negative_sur = (valid_surrounding < 0).sum()
            print(f"• 正面影响(增加通风): {positive_sur} ({positive_sur/len(valid_surrounding)*100:.1f}%)")
            print(f"• 负面影响(减少通风): {negative_sur} ({negative_sur/len(valid_surrounding)*100:.1f}%)")
            
            # 敏感性统计
            sensitivity_counts = comparison_df['Environment_Sensitivity'].value_counts()
            print(f"\n🎯 环境敏感性分布 (稳健分析):")
            for sensitivity, count in sensitivity_counts.items():
                if sensitivity != "数据缺失":
                    print(f"• {sensitivity}: {count} ({count/len(comparison_df)*100:.1f}%)")
        
        return comparison_df
        
    except Exception as e:
        print(f"✗ 稳健环境比较分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    
    print("开始稳健环境数据处理...")
    
    # 定义文件路径和环境信息
    environments = [
        {
            'file': r"D:\XU\2\DATARECORD\GH\huanjing\ach_groups_20250731_150834.csv",
            'name': '孤立建筑',
            'suffix': 'isolated'
        },
        {
            'file': r"D:\XU\2\DATARECORD\GH\huanjing\ach_groups_20250801_221816.csv",
            'name': '前后建筑',
            'suffix': 'front_back'
        },
        {
            'file': r"D:\XU\2\DATARECORD\GH\huanjing\ach_groups_20250802_115209.csv",
            'name': '四周建筑',
            'suffix': 'surrounding'
        }
    ]
    
    # 处理每个环境文件
    processed_count = 0
    for env in environments:
        result = process_environment_file(env['file'], env['name'], env['suffix'])
        if result is not None:
            processed_count += 1
    
    # 如果至少处理了两个文件，进行比较分析
    if processed_count >= 2:
        comparison_df = create_robust_environment_comparison()
        
        if comparison_df is not None:
            print(f"\n{'='*80}")
            print("稳健环境数据处理完成！")
            print("生成的文件:")
            print("• isolated_robust_no_group456.csv - 孤立建筑稳健数据")
            print("• front_back_robust_no_group456.csv - 前后建筑稳健数据")
            print("• surrounding_robust_no_group456.csv - 四周建筑稳健数据")
            print("• robust_environment_impact_analysis.csv - 稳健环境影响分析")
            print(f"{'='*80}")
        else:
            print("稳健环境比较分析失败")
    else:
        print("处理的文件数量不足，无法进行比较分析")

if __name__ == "__main__":
    main()
