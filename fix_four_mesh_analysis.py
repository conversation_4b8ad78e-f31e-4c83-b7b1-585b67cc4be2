import pandas as pd
import numpy as np

def fix_four_mesh_convergence_analysis():
    """修正四网格收敛性分析"""
    
    print("="*80)
    print("修正四网格收敛性分析")
    print("="*80)
    
    try:
        # 读取现有的三网格数据
        df_3mesh = pd.read_csv('three_mesh_convergence_no_group456.csv')
        print(f"✓ 读取三网格数据: {len(df_3mesh)} 个Group")
        
        # 读取ACC=4数据
        df_acc4 = pd.read_csv('accbuilding_4_no_group456.csv')
        print(f"✓ 读取ACC=4数据: {len(df_acc4)} 个Group")
        
        print(f"\n三网格数据列名: {list(df_3mesh.columns)}")
        print(f"ACC=4数据列名: {list(df_acc4.columns)}")
        
        # 合并数据 - 正确的方式
        merged_df = df_3mesh.merge(df_acc4[['Group', 'Robust_ACH']], 
                                  on='Group', how='left')
        
        # 重命名ACC=4的ACH列
        merged_df = merged_df.rename(columns={'Robust_ACH': 'accbuilding_4_no_group456_ACH'})
        
        print(f"✓ 成功合并数据: {len(merged_df)} 个Group")
        
        # 重新计算收敛性分析
        print(f"\n开始四网格收敛性分析...")
        print(f"{'Group':<6} {'ACC=1':<8} {'ACC=2':<8} {'ACC=3':<8} {'ACC=4':<8} {'3→4%':<8} {'收敛状态':<12}")
        print("-" * 70)
        
        convergence_results = []
        
        for _, row in merged_df.iterrows():
            group_id = int(row['Group'])
            ach_1 = row.get('accbuilding_1_no_group456_ACH', np.nan)
            ach_2 = row.get('accbuilding_2_no_group456_ACH', np.nan)
            ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)
            ach_4 = row.get('accbuilding_4_no_group456_ACH', np.nan)
            
            # 计算3→4的变化率
            if not (np.isnan(ach_3) or np.isnan(ach_4)):
                change_3_4 = abs(ach_4 - ach_3) / ach_3 * 100 if ach_3 != 0 else 0
                
                # 判断收敛状态
                if change_3_4 < 1:
                    convergence_status = "完全收敛"
                elif change_3_4 < 3:
                    convergence_status = "良好收敛"
                elif change_3_4 < 5:
                    convergence_status = "可接受"
                else:
                    convergence_status = "需更细网格"
            else:
                change_3_4 = np.nan
                convergence_status = "数据缺失"
            
            print(f"{group_id:<6} {ach_1:<8.2f} {ach_2:<8.2f} {ach_3:<8.2f} {ach_4:<8.2f} {change_3_4:<8.1f} {convergence_status:<12}")
            
            # 添加到结果中
            row_dict = row.to_dict()
            row_dict['Change_3_4_Percent'] = change_3_4
            row_dict['Convergence_Status_4Mesh'] = convergence_status
            convergence_results.append(row_dict)
        
        # 创建四网格结果DataFrame
        four_mesh_df = pd.DataFrame(convergence_results)
        
        # 保存四网格收敛性分析结果
        output_file = 'four_mesh_convergence_no_group456.csv'
        four_mesh_df.to_csv(output_file, index=False)
        print(f"\n✓ 四网格收敛性分析结果已保存: {output_file}")
        
        # 统计汇总
        valid_changes = four_mesh_df['Change_3_4_Percent'].dropna()
        if len(valid_changes) > 0:
            excellent_conv = (valid_changes < 1).sum()
            good_conv = ((valid_changes >= 1) & (valid_changes < 3)).sum()
            acceptable_conv = ((valid_changes >= 3) & (valid_changes < 5)).sum()
            poor_conv = (valid_changes >= 5).sum()
            
            print(f"\n📊 四网格收敛性统计:")
            print(f"• 总Group数: {len(valid_changes)}")
            print(f"• 完全收敛 (<1%): {excellent_conv} ({excellent_conv/len(valid_changes)*100:.1f}%)")
            print(f"• 良好收敛 (1-3%): {good_conv} ({good_conv/len(valid_changes)*100:.1f}%)")
            print(f"• 可接受 (3-5%): {acceptable_conv} ({acceptable_conv/len(valid_changes)*100:.1f}%)")
            print(f"• 需更细网格 (>5%): {poor_conv} ({poor_conv/len(valid_changes)*100:.1f}%)")
            
            # 总体评估
            total_satisfactory = excellent_conv + good_conv
            print(f"• 总体满意率: {total_satisfactory/len(valid_changes)*100:.1f}%")
            
            # 与三网格对比
            print(f"\n🔍 与三网格对比:")
            print(f"• 三网格独立Group数: {(four_mesh_df['Relative_Change_%'] < 5).sum()}")
            print(f"• 四网格收敛Group数: {(valid_changes < 5).sum()}")
            
            # 显示收敛最好的Group
            best_groups = four_mesh_df[four_mesh_df['Change_3_4_Percent'] < 1]['Group'].tolist()
            if best_groups:
                print(f"• 完全收敛的Group: {best_groups}")
        else:
            print(f"\n⚠️ 警告: 没有有效的四网格收敛数据")
        
        return four_mesh_df
        
    except Exception as e:
        print(f"✗ 修正四网格分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_four_mesh_plot():
    """创建四网格ACH变化图"""
    
    print("\n" + "="*80)
    print("创建四网格ACH变化图")
    print("="*80)
    
    try:
        import matplotlib
        matplotlib.use('Agg')
        import matplotlib.pyplot as plt
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 读取四网格数据
        df = pd.read_csv('four_mesh_convergence_no_group456.csv')
        
        # 指定要显示的Group (与之前保持一致)
        selected_groups = [0, 3, 11, 12, 29, 1, 2, 8, 10, 14, 15, 16, 17, 18, 19, 24, 30]
        
        # 准备数据
        groups = []
        ach_1 = []  # ACC=1 粗网格
        ach_2 = []  # ACC=2 中网格
        ach_3 = []  # ACC=3 细网格
        ach_4 = []  # ACC=4 最细网格
        
        for _, row in df.iterrows():
            group_id = int(row['Group'])
            
            if group_id in selected_groups:
                a1 = row.get('accbuilding_1_no_group456_ACH', np.nan)
                a2 = row.get('accbuilding_2_no_group456_ACH', np.nan)
                a3 = row.get('accbuilding_3_no_group456_ACH', np.nan)
                a4 = row.get('accbuilding_4_no_group456_ACH', np.nan)
                
                if not (np.isnan(a1) or np.isnan(a2) or np.isnan(a3) or np.isnan(a4)):
                    groups.append(group_id)
                    ach_1.append(a1)
                    ach_2.append(a2)
                    ach_3.append(a3)
                    ach_4.append(a4)
        
        # 按Group ID排序
        sorted_data = sorted(zip(groups, ach_1, ach_2, ach_3, ach_4))
        groups, ach_1, ach_2, ach_3, ach_4 = zip(*sorted_data)
        
        print(f"✓ 成功提取 {len(groups)} 个Group的四网格数据")
        print(f"Group列表: {list(groups)}")
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(16, 8))
        
        # 创建均匀分布的x轴位置
        x_positions = range(len(groups))
        
        # 定义四种颜色 - 黑灰红 + 深蓝
        colors = {
            'acc1': '#2C2C2C',    # 深灰色/黑色
            'acc2': '#808080',    # 中灰色
            'acc3': '#DC143C',    # 深红色
            'acc4': '#191970'     # 深蓝色
        }
        
        # 绘制四条线
        ax.plot(x_positions, ach_1, 'o-', linewidth=3, markersize=10, 
                color=colors['acc1'], alpha=0.9, label='ACC=1 (粗网格)', 
                markerfacecolor='white', markeredgewidth=2.5)
        
        ax.plot(x_positions, ach_2, 's--', linewidth=3, markersize=10, 
                color=colors['acc2'], alpha=0.9, label='ACC=2 (中网格)', 
                markerfacecolor='white', markeredgewidth=2.5)
        
        ax.plot(x_positions, ach_3, '^-', linewidth=3, markersize=10, 
                color=colors['acc3'], alpha=0.9, label='ACC=3 (细网格)', 
                markerfacecolor='white', markeredgewidth=2.5)
        
        ax.plot(x_positions, ach_4, 'd-', linewidth=3, markersize=10, 
                color=colors['acc4'], alpha=0.9, label='ACC=4 (最细网格)', 
                markerfacecolor='white', markeredgewidth=2.5)
        
        # 设置坐标轴
        ax.set_xlabel('Group ID', fontsize=16, fontweight='bold')
        ax.set_ylabel('ACH (h⁻¹)', fontsize=16, fontweight='bold')
        ax.set_title('四网格ACH在网格细化过程中的变化', fontsize=18, fontweight='bold', pad=25)
        
        # 设置x轴刻度
        ax.set_xticks(x_positions)
        ax.set_xticklabels(groups, fontsize=14, fontweight='bold')
        
        # 设置y轴
        ax.tick_params(axis='y', labelsize=14)
        
        # 添加网格
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.8, color='#CCCCCC')
        ax.set_axisbelow(True)
        
        # 添加图例
        legend = ax.legend(fontsize=14, loc='upper left', frameon=True, fancybox=True, 
                          shadow=True, borderpad=1, columnspacing=1)
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.95)
        
        # 设置图表边框
        for spine in ax.spines.values():
            spine.set_linewidth(2)
            spine.set_color('#666666')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        plt.savefig('four_mesh_ach_refinement.png', dpi=300, bbox_inches='tight', 
                    facecolor='white', edgecolor='none')
        print(f"📊 四网格ACH变化图已保存: four_mesh_ach_refinement.png")
        
        return fig
        
    except Exception as e:
        print(f"✗ 创建四网格图表失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    
    print("开始修正四网格收敛性分析...")
    
    # 修正四网格收敛性分析
    four_mesh_results = fix_four_mesh_convergence_analysis()
    
    if four_mesh_results is not None:
        # 创建四网格图表
        create_four_mesh_plot()
        
        print(f"\n{'='*80}")
        print("四网格分析修正完成！")
        print("生成的文件:")
        print("• four_mesh_convergence_no_group456.csv - 修正的四网格收敛性分析")
        print("• four_mesh_ach_refinement.png - 四网格ACH变化图")
        print(f"{'='*80}")
    else:
        print("四网格分析修正失败")

if __name__ == "__main__":
    main()
