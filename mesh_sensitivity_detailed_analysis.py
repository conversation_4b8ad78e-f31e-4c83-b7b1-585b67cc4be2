import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_mesh_sensitivity_detailed():
    """详细的网格敏感性分析"""
    
    print("="*80)
    print("详细网格敏感性分析报告")
    print("网格精度: ACC=1(粗) → ACC=2(中) → ACC=3(细)")
    print("="*80)
    
    # 读取三网格收敛性分析结果
    try:
        df = pd.read_csv('three_mesh_convergence_analysis.csv')
        print(f"✓ 读取收敛性分析数据: {len(df)} 个Group")
    except Exception as e:
        print(f"✗ 读取数据失败: {e}")
        return
    
    # 分析各种收敛模式
    convergence_patterns = {
        'monotonic_converging': [],      # 单调收敛
        'monotonic_diverging': [],       # 单调发散
        'oscillating': [],               # 振荡
        'converged_final': [],           # 最终收敛
        'highly_sensitive': []           # 高敏感性
    }
    
    print(f"\n📊 逐Group网格敏感性分析:")
    print(f"{'Group':<6} {'体积':<8} {'粗网格':<10} {'中网格':<10} {'细网格':<10} {'变化%':<8} {'模式':<12} {'建议':<15}")
    print("-" * 95)
    
    for _, row in df.iterrows():
        group_id = int(row['Group'])
        volume = row['Volume'] if pd.notna(row['Volume']) else 0
        
        # 获取ACH值 (粗→中→细)
        ach_1 = row.get('accbuilding_1_no_group6_ACH', np.nan)  # 粗
        ach_2 = row.get('accbuilding_2_no_group6_ACH', np.nan)  # 中
        ach_3 = row.get('accbuilding_3_no_group6_ACH', np.nan)  # 细
        
        relative_change = row.get('Relative_Change_%', 0)
        converged = row.get('Converged', False)
        
        # 分析收敛模式
        pattern = "未知"
        recommendation = "检查数据"
        
        if not (np.isnan(ach_1) or np.isnan(ach_2) or np.isnan(ach_3)):
            # 计算变化率
            change_1_2 = abs(ach_2 - ach_1) / ach_1 * 100 if ach_1 != 0 else 0
            change_2_3 = abs(ach_3 - ach_2) / ach_2 * 100 if ach_2 != 0 else 0
            
            # 判断收敛模式
            if converged:
                if change_2_3 < change_1_2 * 0.5:
                    pattern = "单调收敛✓"
                    recommendation = "网格充足"
                    convergence_patterns['monotonic_converging'].append(group_id)
                    convergence_patterns['converged_final'].append(group_id)
                else:
                    pattern = "最终收敛✓"
                    recommendation = "可接受"
                    convergence_patterns['converged_final'].append(group_id)
            else:
                if relative_change > 30:
                    pattern = "高敏感❌"
                    recommendation = "急需细化"
                    convergence_patterns['highly_sensitive'].append(group_id)
                elif change_2_3 > change_1_2 * 1.5:
                    pattern = "单调发散❌"
                    recommendation = "继续细化"
                    convergence_patterns['monotonic_diverging'].append(group_id)
                elif abs(change_2_3 - change_1_2) < change_1_2 * 0.3:
                    pattern = "振荡~"
                    recommendation = "检查数值"
                    convergence_patterns['oscillating'].append(group_id)
                else:
                    pattern = "缓慢收敛⚠️"
                    recommendation = "建议细化"
        
        print(f"{group_id:<6} {volume:<8.1f} {ach_1:<10.3f} {ach_2:<10.3f} {ach_3:<10.3f} "
              f"{relative_change:<8.2f} {pattern:<12} {recommendation:<15}")
    
    # 统计各种模式
    print(f"\n📈 收敛模式统计:")
    total_groups = len(df)
    
    print(f"  🟢 单调收敛: {len(convergence_patterns['monotonic_converging'])} 个 "
          f"({len(convergence_patterns['monotonic_converging'])/total_groups*100:.1f}%)")
    if convergence_patterns['monotonic_converging']:
        print(f"     Group: {convergence_patterns['monotonic_converging']}")
    
    print(f"  🟡 最终收敛: {len(convergence_patterns['converged_final'])} 个 "
          f"({len(convergence_patterns['converged_final'])/total_groups*100:.1f}%)")
    if convergence_patterns['converged_final']:
        print(f"     Group: {convergence_patterns['converged_final']}")
    
    print(f"  🔴 单调发散: {len(convergence_patterns['monotonic_diverging'])} 个 "
          f"({len(convergence_patterns['monotonic_diverging'])/total_groups*100:.1f}%)")
    if convergence_patterns['monotonic_diverging']:
        print(f"     Group: {convergence_patterns['monotonic_diverging']}")
    
    print(f"  🟠 振荡模式: {len(convergence_patterns['oscillating'])} 个 "
          f"({len(convergence_patterns['oscillating'])/total_groups*100:.1f}%)")
    if convergence_patterns['oscillating']:
        print(f"     Group: {convergence_patterns['oscillating']}")
    
    print(f"  🔴 高敏感性: {len(convergence_patterns['highly_sensitive'])} 个 "
          f"({len(convergence_patterns['highly_sensitive'])/total_groups*100:.1f}%)")
    if convergence_patterns['highly_sensitive']:
        print(f"     Group: {convergence_patterns['highly_sensitive']}")
    
    # 网格质量评估
    print(f"\n🎯 网格质量评估:")
    
    good_groups = len(convergence_patterns['converged_final'])
    problematic_groups = len(convergence_patterns['highly_sensitive']) + len(convergence_patterns['monotonic_diverging'])
    
    if good_groups >= total_groups * 0.5:
        quality_level = "良好"
        quality_icon = "✅"
    elif good_groups >= total_groups * 0.3:
        quality_level = "可接受"
        quality_icon = "⚠️"
    else:
        quality_level = "需要改善"
        quality_icon = "❌"
    
    print(f"  整体网格质量: {quality_icon} {quality_level}")
    print(f"  收敛Group比例: {good_groups/total_groups*100:.1f}%")
    print(f"  问题Group比例: {problematic_groups/total_groups*100:.1f}%")
    
    # 细化建议
    print(f"\n🔧 网格细化建议:")
    
    if len(convergence_patterns['highly_sensitive']) > 0:
        print(f"  🚨 优先级1 - 高敏感性Group (急需细化):")
        for group_id in convergence_patterns['highly_sensitive']:
            group_data = df[df['Group'] == group_id].iloc[0]
            ach_range = f"{group_data['Min_ACH']:.2f}-{group_data['Max_ACH']:.2f}"
            print(f"     Group {group_id}: ACH变化范围 {ach_range}, 变化{group_data['Relative_Change_%']:.1f}%")
    
    if len(convergence_patterns['monotonic_diverging']) > 0:
        print(f"  ⚠️  优先级2 - 单调发散Group (建议细化):")
        for group_id in convergence_patterns['monotonic_diverging']:
            group_data = df[df['Group'] == group_id].iloc[0]
            print(f"     Group {group_id}: 需要继续细化网格")
    
    if len(convergence_patterns['oscillating']) > 0:
        print(f"  🔍 优先级3 - 振荡Group (检查数值方法):")
        for group_id in convergence_patterns['oscillating']:
            print(f"     Group {group_id}: 检查数值格式、边界条件或湍流模型")
    
    # 总体建议
    print(f"\n📋 总体建议:")
    
    if len(convergence_patterns['converged_final']) >= total_groups * 0.5:
        print(f"  ✅ 当前网格精度对大部分区域已足够")
        print(f"  🎯 建议: 对{len(convergence_patterns['highly_sensitive']) + len(convergence_patterns['monotonic_diverging'])}个问题Group进行局部细化")
    else:
        print(f"  ❌ 当前网格精度不足")
        print(f"  🎯 建议: 全面细化至ACC=4或更高精度")
        print(f"  📈 预期: 细化后收敛率应提升至50%以上")
    
    if len(convergence_patterns['oscillating']) > total_groups * 0.2:
        print(f"  ⚠️  振荡Group较多，建议检查:")
        print(f"     - 数值格式设置")
        print(f"     - 边界条件定义")
        print(f"     - 湍流模型选择")
        print(f"     - 时间步长设置")
    
    return convergence_patterns

def plot_convergence_trends():
    """绘制收敛趋势图"""
    
    try:
        df = pd.read_csv('three_mesh_convergence_analysis.csv')
    except:
        print("无法读取数据文件")
        return
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('网格敏感性详细分析 (ACC=1粗 → ACC=2中 → ACC=3细)', fontsize=16, fontweight='bold')
    
    # 1. 收敛趋势分类
    ax1 = axes[0, 0]
    
    groups = df['Group'].values
    changes = df['Relative_Change_%'].values
    
    # 按变化程度分类
    colors = []
    for change in changes:
        if change < 5:
            colors.append('green')      # 已收敛
        elif change < 15:
            colors.append('orange')     # 接近收敛
        elif change < 30:
            colors.append('red')        # 需要细化
        else:
            colors.append('darkred')    # 高敏感性
    
    bars = ax1.bar(groups, changes, color=colors, alpha=0.7)
    ax1.axhline(y=5, color='green', linestyle='--', alpha=0.8, label='收敛线(5%)')
    ax1.axhline(y=15, color='orange', linestyle='--', alpha=0.8, label='可接受线(15%)')
    ax1.axhline(y=30, color='red', linestyle='--', alpha=0.8, label='高敏感线(30%)')
    
    ax1.set_xlabel('Group ID')
    ax1.set_ylabel('相对变化 (%)')
    ax1.set_title('各Group网格敏感性分级')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. ACH值变化趋势
    ax2 = axes[0, 1]
    
    # 选择几个典型Group展示
    typical_groups = [0, 4, 7, 20, 29]  # 代表不同收敛模式
    mesh_levels = [1, 2, 3]  # 粗→中→细
    
    for group_id in typical_groups:
        if group_id in groups:
            group_data = df[df['Group'] == group_id].iloc[0]
            ach_values = [
                group_data.get('accbuilding_1_no_group6_ACH', np.nan),
                group_data.get('accbuilding_2_no_group6_ACH', np.nan),
                group_data.get('accbuilding_3_no_group6_ACH', np.nan)
            ]
            
            if not any(np.isnan(ach_values)):
                ax2.plot(mesh_levels, ach_values, 'o-', label=f'Group {group_id}', linewidth=2, markersize=6)
    
    ax2.set_xlabel('网格精度 (1=粗, 2=中, 3=细)')
    ax2.set_ylabel('ACH (次/小时)')
    ax2.set_title('典型Group的ACH收敛趋势')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_xticks([1, 2, 3])
    ax2.set_xticklabels(['ACC=1\n(粗)', 'ACC=2\n(中)', 'ACC=3\n(细)'])
    
    # 3. 收敛模式分布饼图
    ax3 = axes[1, 0]
    
    converged_count = sum(df['Converged'])
    high_sensitive = sum(df['Relative_Change_%'] > 30)
    moderate_sensitive = sum((df['Relative_Change_%'] >= 15) & (df['Relative_Change_%'] <= 30))
    low_sensitive = sum((df['Relative_Change_%'] >= 5) & (df['Relative_Change_%'] < 15))
    
    labels = ['已收敛(<5%)', '低敏感(5-15%)', '中敏感(15-30%)', '高敏感(>30%)']
    sizes = [converged_count, low_sensitive, moderate_sensitive, high_sensitive]
    colors = ['lightgreen', 'lightyellow', 'lightcoral', 'darkred']
    
    wedges, texts, autotexts = ax3.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax3.set_title('网格敏感性分布')
    
    # 4. 网格细化效果
    ax4 = axes[1, 1]
    
    # 计算相邻网格间的变化率
    change_1_2 = []  # 粗→中
    change_2_3 = []  # 中→细
    
    for _, row in df.iterrows():
        ach_1 = row.get('accbuilding_1_no_group6_ACH', np.nan)
        ach_2 = row.get('accbuilding_2_no_group6_ACH', np.nan)
        ach_3 = row.get('accbuilding_3_no_group6_ACH', np.nan)
        
        if not (np.isnan(ach_1) or np.isnan(ach_2) or np.isnan(ach_3)):
            if ach_1 != 0:
                change_1_2.append(abs(ach_2 - ach_1) / ach_1 * 100)
            if ach_2 != 0:
                change_2_3.append(abs(ach_3 - ach_2) / ach_2 * 100)
    
    x = np.arange(len(change_1_2))
    width = 0.35
    
    bars1 = ax4.bar(x - width/2, change_1_2, width, label='粗→中网格变化', alpha=0.7, color='orange')
    bars2 = ax4.bar(x + width/2, change_2_3, width, label='中→细网格变化', alpha=0.7, color='blue')
    
    ax4.set_xlabel('Group ID')
    ax4.set_ylabel('ACH变化率 (%)')
    ax4.set_title('网格细化效果对比')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('mesh_sensitivity_detailed_analysis.png', dpi=300, bbox_inches='tight')
    print(f"📊 详细敏感性分析图表已保存: mesh_sensitivity_detailed_analysis.png")

def main():
    """主函数"""
    
    # 执行详细分析
    convergence_patterns = analyze_mesh_sensitivity_detailed()
    
    # 绘制图表
    plot_convergence_trends()
    
    print(f"\n💾 分析完成，已生成详细报告和图表")

if __name__ == "__main__":
    main()
