import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_ach_grid_refinement_plot():
    """创建指定Group的ACH在网格细化中的变化图"""
    
    print("="*80)
    print("创建ACH在网格细化中的变化图")
    print("="*80)
    
    try:
        # 读取三网格收敛性分析数据
        df = pd.read_csv('three_mesh_convergence_no_group456.csv')
        print(f"✓ 读取收敛性分析数据: {len(df)} 个Group")
    except Exception as e:
        print(f"✗ 读取数据失败: {e}")
        return
    
    # 指定要保留的Group ID
    selected_groups = [0, 3, 11, 12, 29, 1, 2, 8, 10, 14, 15, 16, 17, 18, 19, 24, 30]
    print(f"选择的Group ID: {selected_groups}")
    
    # 准备数据
    groups = []
    ach_coarse = []  # ACC=1 粗网格
    ach_medium = []  # ACC=2 中网格
    ach_fine = []    # ACC=3 细网格
    
    for _, row in df.iterrows():
        group_id = int(row['Group'])
        
        # 只保留指定的Group
        if group_id in selected_groups:
            # 获取ACH值
            ach_1 = row.get('accbuilding_1_no_group456_ACH', np.nan)  # 粗
            ach_2 = row.get('accbuilding_2_no_group456_ACH', np.nan)  # 中
            ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)  # 细
            
            if not (np.isnan(ach_1) or np.isnan(ach_2) or np.isnan(ach_3)):
                groups.append(group_id)
                ach_coarse.append(ach_1)
                ach_medium.append(ach_2)
                ach_fine.append(ach_3)
    
    # 按Group ID排序
    sorted_data = sorted(zip(groups, ach_coarse, ach_medium, ach_fine))
    groups, ach_coarse, ach_medium, ach_fine = zip(*sorted_data)
    
    print(f"✓ 成功提取 {len(groups)} 个Group的数据")
    print(f"Group列表: {list(groups)}")
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(14, 8))

    # 创建均匀分布的x轴位置
    x_positions = range(len(groups))  # 0, 1, 2, 3, ... 均匀分布

    # 绘制三条线 - 使用黑灰红配色方案
    line1 = ax.plot(x_positions, ach_coarse, 'o-', linewidth=2.5, markersize=8,
                    color='#2C2C2C', alpha=0.9, label='ACC=1 (粗网格)', markerfacecolor='white', markeredgewidth=2)
    line2 = ax.plot(x_positions, ach_medium, 's--', linewidth=2.5, markersize=8,
                    color='#808080', alpha=0.9, label='ACC=2 (中网格)', markerfacecolor='white', markeredgewidth=2)
    line3 = ax.plot(x_positions, ach_fine, '^-', linewidth=2.5, markersize=8,
                    color='#DC143C', alpha=0.9, label='ACC=3 (细网格)', markerfacecolor='white', markeredgewidth=2)

    # 设置坐标轴
    ax.set_xlabel('Group ID', fontsize=14, fontweight='bold')
    ax.set_ylabel('ACH (h⁻¹)', fontsize=14, fontweight='bold')
    ax.set_title('ACH在网格细化过程中的变化', fontsize=16, fontweight='bold', pad=20)

    # 设置x轴刻度 - 均匀分布但显示实际Group ID
    ax.set_xticks(x_positions)
    ax.set_xticklabels(groups, fontsize=12)
    
    # 设置y轴
    ax.tick_params(axis='y', labelsize=12)
    
    # 添加网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # 添加图例
    legend = ax.legend(fontsize=12, loc='upper left', frameon=True, fancybox=True, shadow=True)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_alpha(0.9)
    
    # 设置图表边框
    for spine in ax.spines.values():
        spine.set_linewidth(1.5)
    
    # 添加数值标注（可选，对于重要的点）
    # 标注一些关键点的数值
    for i, group_id in enumerate(groups):
        if group_id in [0, 3, 11, 12, 29]:  # 收敛较好的Group
            # 在细网格点上标注数值 - 使用均匀分布的x位置
            ax.annotate(f'{ach_fine[i]:.1f}',
                       xy=(x_positions[i], ach_fine[i]),
                       xytext=(5, 10), textcoords='offset points',
                       fontsize=9, ha='left', va='bottom',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('ach_grid_refinement_selected_groups.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    print(f"📊 ACH网格细化变化图已保存: ach_grid_refinement_selected_groups.png")
    
    # 显示统计信息
    print(f"\n📊 数据统计:")
    print(f"{'Group':<6} {'ACC=1':<8} {'ACC=2':<8} {'ACC=3':<8} {'粗→中%':<8} {'中→细%':<8}")
    print("-" * 50)
    
    for i, group_id in enumerate(groups):
        change_1_2 = abs(ach_medium[i] - ach_coarse[i]) / ach_coarse[i] * 100 if ach_coarse[i] != 0 else 0
        change_2_3 = abs(ach_fine[i] - ach_medium[i]) / ach_medium[i] * 100 if ach_medium[i] != 0 else 0
        
        print(f"{group_id:<6} {ach_coarse[i]:<8.2f} {ach_medium[i]:<8.2f} {ach_fine[i]:<8.2f} "
              f"{change_1_2:<8.1f} {change_2_3:<8.1f}")
    
    return fig

def create_detailed_analysis_plot():
    """创建详细的分析图表，包含变化率信息"""
    
    try:
        df = pd.read_csv('three_mesh_convergence_no_group456.csv')
    except:
        print("无法读取数据进行详细分析")
        return
    
    # 指定要保留的Group ID
    selected_groups = [0, 3, 11, 12, 29, 1, 2, 8, 10, 14, 15, 16, 17, 18, 19, 24, 30]
    
    # 准备数据
    groups = []
    ach_coarse = []
    ach_medium = []
    ach_fine = []
    
    for _, row in df.iterrows():
        group_id = int(row['Group'])
        
        if group_id in selected_groups:
            ach_1 = row.get('accbuilding_1_no_group456_ACH', np.nan)
            ach_2 = row.get('accbuilding_2_no_group456_ACH', np.nan)
            ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)
            
            if not (np.isnan(ach_1) or np.isnan(ach_2) or np.isnan(ach_3)):
                groups.append(group_id)
                ach_coarse.append(ach_1)
                ach_medium.append(ach_2)
                ach_fine.append(ach_3)
    
    # 按Group ID排序
    sorted_data = sorted(zip(groups, ach_coarse, ach_medium, ach_fine))
    groups, ach_coarse, ach_medium, ach_fine = zip(*sorted_data)
    
    # 创建双子图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))
    fig.suptitle('选定Group的ACH网格敏感性详细分析', fontsize=16, fontweight='bold')
    
    # 上图：ACH值变化
    ax1.plot(groups, ach_coarse, 'o-', linewidth=2.5, markersize=8, 
             color='red', alpha=0.8, label='ACC=1 (粗网格)', markerfacecolor='white', markeredgewidth=2)
    ax1.plot(groups, ach_medium, 's--', linewidth=2.5, markersize=8, 
             color='blue', alpha=0.8, label='ACC=2 (中网格)', markerfacecolor='white', markeredgewidth=2)
    ax1.plot(groups, ach_fine, '^-', linewidth=2.5, markersize=8, 
             color='green', alpha=0.8, label='ACC=3 (细网格)', markerfacecolor='white', markeredgewidth=2)
    
    ax1.set_xlabel('Group ID', fontsize=12, fontweight='bold')
    ax1.set_ylabel('ACH (h⁻¹)', fontsize=12, fontweight='bold')
    ax1.set_title('(a) ACH值在不同网格精度下的变化', fontsize=14)
    ax1.set_xticks(groups)
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    
    # 下图：变化率分析
    change_2_3 = []
    for i in range(len(groups)):
        change = abs(ach_fine[i] - ach_medium[i]) / ach_medium[i] * 100 if ach_medium[i] != 0 else 0
        change_2_3.append(change)
    
    # 根据网格独立性着色
    colors = []
    for change in change_2_3:
        if change < 5:
            colors.append('green')  # 网格独立
        elif change < 10:
            colors.append('orange')  # 边界情况
        else:
            colors.append('red')  # 需要细化
    
    bars = ax2.bar(groups, change_2_3, color=colors, alpha=0.7, edgecolor='black', linewidth=1)
    ax2.axhline(y=5, color='red', linestyle='--', alpha=0.8, linewidth=2, label='网格独立标准 (5%)')
    ax2.axhline(y=10, color='orange', linestyle='--', alpha=0.8, linewidth=2, label='可接受标准 (10%)')
    
    # 在柱子上添加数值标签
    for bar, change in zip(bars, change_2_3):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                f'{change:.1f}%', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    ax2.set_xlabel('Group ID', fontsize=12, fontweight='bold')
    ax2.set_ylabel('中→细 ACH变化率 (%)', fontsize=12, fontweight='bold')
    ax2.set_title('(b) 网格独立性分析 (中→细变化率)', fontsize=14)
    ax2.set_xticks(groups)
    ax2.legend(fontsize=11)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('ach_detailed_analysis_selected_groups.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    print(f"📊 详细分析图已保存: ach_detailed_analysis_selected_groups.png")
    
    return fig

def create_convergence_classification_plot():
    """创建收敛性分类图表"""
    
    try:
        df = pd.read_csv('three_mesh_convergence_no_group456.csv')
    except:
        print("无法读取数据进行分类分析")
        return
    
    # 指定要保留的Group ID
    selected_groups = [0, 3, 11, 12, 29, 1, 2, 8, 10, 14, 15, 16, 17, 18, 19, 24, 30]
    
    # 分类Group
    excellent_groups = [0, 3, 11, 12, 29]  # 收敛良好
    other_groups = [1, 2, 8, 10, 14, 15, 16, 17, 18, 19, 24, 30]  # 其他Group
    
    # 准备数据
    def get_group_data(group_list):
        groups = []
        ach_coarse = []
        ach_medium = []
        ach_fine = []
        
        for _, row in df.iterrows():
            group_id = int(row['Group'])
            
            if group_id in group_list:
                ach_1 = row.get('accbuilding_1_no_group456_ACH', np.nan)
                ach_2 = row.get('accbuilding_2_no_group456_ACH', np.nan)
                ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)
                
                if not (np.isnan(ach_1) or np.isnan(ach_2) or np.isnan(ach_3)):
                    groups.append(group_id)
                    ach_coarse.append(ach_1)
                    ach_medium.append(ach_2)
                    ach_fine.append(ach_3)
        
        # 排序
        sorted_data = sorted(zip(groups, ach_coarse, ach_medium, ach_fine))
        return zip(*sorted_data)
    
    # 创建分类对比图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    fig.suptitle('收敛性分类对比分析', fontsize=16, fontweight='bold')
    
    # 收敛良好的Group
    groups1, ach_coarse1, ach_medium1, ach_fine1 = get_group_data(excellent_groups)
    
    ax1.plot(groups1, ach_coarse1, 'o-', linewidth=2.5, markersize=8, 
             color='red', alpha=0.8, label='ACC=1 (粗网格)')
    ax1.plot(groups1, ach_medium1, 's--', linewidth=2.5, markersize=8, 
             color='blue', alpha=0.8, label='ACC=2 (中网格)')
    ax1.plot(groups1, ach_fine1, '^-', linewidth=2.5, markersize=8, 
             color='green', alpha=0.8, label='ACC=3 (细网格)')
    
    ax1.set_xlabel('Group ID', fontsize=12)
    ax1.set_ylabel('ACH (h⁻¹)', fontsize=12)
    ax1.set_title('(a) 收敛良好的Group (0, 3, 11, 12, 29)', fontsize=14, color='green')
    ax1.set_xticks(groups1)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 其他Group
    groups2, ach_coarse2, ach_medium2, ach_fine2 = get_group_data(other_groups)
    
    ax2.plot(groups2, ach_coarse2, 'o-', linewidth=2.5, markersize=6, 
             color='red', alpha=0.8, label='ACC=1 (粗网格)')
    ax2.plot(groups2, ach_medium2, 's--', linewidth=2.5, markersize=6, 
             color='blue', alpha=0.8, label='ACC=2 (中网格)')
    ax2.plot(groups2, ach_fine2, '^-', linewidth=2.5, markersize=6, 
             color='green', alpha=0.8, label='ACC=3 (细网格)')
    
    ax2.set_xlabel('Group ID', fontsize=12)
    ax2.set_ylabel('ACH (h⁻¹)', fontsize=12)
    ax2.set_title('(b) 其他Group', fontsize=14, color='orange')
    ax2.set_xticks(groups2)
    ax2.set_xticklabels(groups2, rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('ach_convergence_classification.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    print(f"📊 收敛性分类图已保存: ach_convergence_classification.png")
    
    return fig

def main():
    """主函数"""
    
    print("开始创建指定Group的ACH网格细化变化图...")
    
    # 创建主要的ACH变化图
    create_ach_grid_refinement_plot()
    
    # 创建详细分析图
    create_detailed_analysis_plot()
    
    # 创建分类对比图
    create_convergence_classification_plot()
    
    print(f"\n{'='*80}")
    print("ACH网格细化变化图创建完成！")
    print("生成的图表:")
    print("• ach_grid_refinement_selected_groups.png - 主要ACH变化图")
    print("• ach_detailed_analysis_selected_groups.png - 详细分析图")
    print("• ach_convergence_classification.png - 收敛性分类对比图")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
