import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
import os
import glob

class MeshSensitivityAnalyzer:
    """网格敏感性分析工具"""
    
    def __init__(self):
        self.mesh_results = {}
        self.analysis_method = 'median'  # 默认使用中位数方法（最robust）
        
    def detect_outliers(self, data, method='iqr', threshold=1.5):
        """检测异常值"""
        if len(data) <= 1:
            return np.zeros(len(data), dtype=bool)
            
        if method == 'iqr':
            Q1 = np.percentile(data, 25)
            Q3 = np.percentile(data, 75)
            IQR = Q3 - Q1
            lower_bound = Q1 - threshold * IQR
            upper_bound = Q3 + threshold * IQR
            outliers = (data < lower_bound) | (data > upper_bound)
            
        elif method == 'zscore':
            z_scores = np.abs(stats.zscore(data))
            outliers = z_scores > threshold
            
        elif method == 'modified_zscore':
            median = np.median(data)
            mad = np.median(np.abs(data - median))
            if mad == 0:
                return np.zeros(len(data), dtype=bool)
            modified_z_scores = 0.6745 * (data - median) / mad
            outliers = np.abs(modified_z_scores) > threshold
        
        return outliers
    
    def clean_probe_data(self, flow_rates, areas=None, outlier_method='iqr', outlier_threshold=1.5):
        """清理探针数据，移除异常值"""
        if areas is None:
            areas = np.ones(len(flow_rates))
            
        abs_flow_rates = np.abs(flow_rates)
        outliers = self.detect_outliers(abs_flow_rates, method=outlier_method, threshold=outlier_threshold)
        
        # 确保至少保留1个探针
        if np.sum(~outliers) == 0:
            outliers = np.zeros(len(flow_rates), dtype=bool)
            
        clean_flow_rates = flow_rates[~outliers]
        clean_areas = areas[~outliers]
        
        return clean_flow_rates, clean_areas, np.sum(outliers)
    
    def calculate_ach_methods(self, flow_rates, areas, volume):
        """计算多种ACH方法"""
        methods = {}
        
        # 方法1: 平均绝对流率
        Q1 = np.mean(np.abs(flow_rates)) * 3600
        methods['mean'] = Q1 / volume if volume != 0 else 0
        
        # 方法2: 中位数方法（最robust）
        Q2 = np.median(np.abs(flow_rates)) * 3600
        methods['median'] = Q2 / volume if volume != 0 else 0
        
        # 方法3: 面积加权平均
        if len(areas) == len(flow_rates) and np.sum(areas) > 0:
            weighted_flow = np.average(np.abs(flow_rates), weights=areas)
            Q3 = weighted_flow * 3600
            methods['weighted'] = Q3 / volume if volume != 0 else 0
        
        # 方法4: 总体积流率
        if len(areas) == len(flow_rates):
            Q4 = np.sum(np.abs(flow_rates * areas)) * 3600
            methods['total_volume'] = Q4 / volume if volume != 0 else 0
        
        # 方法5: 截断平均（去除10%极值）
        sorted_flows = np.sort(np.abs(flow_rates))
        trim_percent = 0.1
        trim_count = int(len(sorted_flows) * trim_percent)
        if trim_count > 0 and len(sorted_flows) > 2 * trim_count:
            trimmed_flows = sorted_flows[trim_count:-trim_count]
        else:
            trimmed_flows = sorted_flows
        Q5 = np.mean(trimmed_flows) * 3600
        methods['trimmed'] = Q5 / volume if volume != 0 else 0
        
        return methods
    
    def analyze_single_mesh(self, df, mesh_name, outlier_method='iqr', outlier_threshold=1.5):
        """分析单个网格的ACH"""
        print(f"\n分析网格: {mesh_name}")
        print("-" * 40)
        
        grouped = df.groupby('GroupID')
        mesh_results = {}
        
        for group_id, group_data in grouped:
            # 获取数据
            flow_rates = group_data['FlowRate'].values
            areas = group_data['Area'].values if 'Area' in group_data.columns else np.ones(len(flow_rates))
            volume = group_data['Volume'].iloc[0]
            
            print(f"  Group {group_id}: {len(flow_rates)} 个探针, 体积={volume:.3f} m³")
            
            # 清理数据
            clean_flows, clean_areas, outlier_count = self.clean_probe_data(
                flow_rates, areas, outlier_method, outlier_threshold
            )
            
            if outlier_count > 0:
                print(f"    移除 {outlier_count} 个异常值")
            
            # 计算ACH
            ach_methods = self.calculate_ach_methods(clean_flows, clean_areas, volume)
            
            # 选择推荐方法
            recommended_ach = ach_methods.get(self.analysis_method, ach_methods['median'])
            
            mesh_results[group_id] = {
                'volume': volume,
                'total_probes': len(flow_rates),
                'valid_probes': len(clean_flows),
                'outliers_removed': outlier_count,
                'ach_methods': ach_methods,
                'recommended_ach': recommended_ach
            }
            
            print(f"    有效探针: {len(clean_flows)}, 推荐ACH: {recommended_ach:.6f}")
        
        self.mesh_results[mesh_name] = mesh_results
        return mesh_results
    
    def compare_meshes(self):
        """比较不同网格的ACH结果"""
        if len(self.mesh_results) < 2:
            print("需要至少2个网格进行比较")
            return None
            
        print(f"\n{'='*60}")
        print("网格敏感性分析结果")
        print(f"{'='*60}")
        
        # 创建比较表格
        comparison_data = []
        
        # 获取所有group IDs
        all_groups = set()
        for mesh_results in self.mesh_results.values():
            all_groups.update(mesh_results.keys())
        
        for group_id in sorted(all_groups):
            for mesh_name, mesh_results in self.mesh_results.items():
                if group_id in mesh_results:
                    result = mesh_results[group_id]
                    comparison_data.append({
                        'Group': group_id,
                        'Mesh': mesh_name,
                        'Volume': result['volume'],
                        'Valid_Probes': result['valid_probes'],
                        'ACH': result['recommended_ach'],
                        'Method': self.analysis_method
                    })
        
        comparison_df = pd.DataFrame(comparison_data)
        
        # 计算网格收敛性
        convergence_results = []
        
        for group_id in sorted(all_groups):
            group_data = comparison_df[comparison_df['Group'] == group_id]
            if len(group_data) >= 2:
                ach_values = group_data['ACH'].values
                mesh_names = group_data['Mesh'].values
                
                # 计算相对变化
                max_ach = np.max(ach_values)
                min_ach = np.min(ach_values)
                relative_change = (max_ach - min_ach) / min_ach * 100 if min_ach != 0 else 0
                
                # 判断收敛性
                is_converged = relative_change < 5.0  # 5%阈值
                
                convergence_results.append({
                    'Group': group_id,
                    'Min_ACH': min_ach,
                    'Max_ACH': max_ach,
                    'Relative_Change_%': relative_change,
                    'Converged': is_converged,
                    'Meshes': ', '.join(mesh_names)
                })
        
        convergence_df = pd.DataFrame(convergence_results)
        
        # 输出结果
        print("\n网格比较结果:")
        print(comparison_df.to_string(index=False))
        
        print(f"\n网格收敛性分析:")
        print(convergence_df.to_string(index=False))
        
        # 保存结果
        comparison_df.to_csv('mesh_comparison.csv', index=False)
        convergence_df.to_csv('mesh_convergence.csv', index=False)
        
        return comparison_df, convergence_df
    
    def plot_convergence(self, save_plot=True):
        """绘制网格收敛图"""
        if len(self.mesh_results) < 2:
            print("需要至少2个网格进行绘图")
            return
            
        # 获取所有group IDs
        all_groups = set()
        for mesh_results in self.mesh_results.values():
            all_groups.update(mesh_results.keys())
        
        fig, axes = plt.subplots(len(all_groups), 1, figsize=(10, 6*len(all_groups)))
        if len(all_groups) == 1:
            axes = [axes]
        
        for i, group_id in enumerate(sorted(all_groups)):
            mesh_names = []
            ach_values = []
            
            for mesh_name, mesh_results in self.mesh_results.items():
                if group_id in mesh_results:
                    mesh_names.append(mesh_name)
                    ach_values.append(mesh_results[group_id]['recommended_ach'])
            
            axes[i].plot(range(len(mesh_names)), ach_values, 'o-', linewidth=2, markersize=8)
            axes[i].set_xlabel('网格')
            axes[i].set_ylabel('ACH')
            axes[i].set_title(f'Group {group_id} - 网格收敛性')
            axes[i].set_xticks(range(len(mesh_names)))
            axes[i].set_xticklabels(mesh_names, rotation=45)
            axes[i].grid(True, alpha=0.3)
            
            # 添加5%收敛线
            if len(ach_values) > 1:
                mean_ach = np.mean(ach_values)
                axes[i].axhline(y=mean_ach*1.05, color='r', linestyle='--', alpha=0.5, label='+5%')
                axes[i].axhline(y=mean_ach*0.95, color='r', linestyle='--', alpha=0.5, label='-5%')
                axes[i].legend()
        
        plt.tight_layout()
        
        if save_plot:
            plt.savefig('mesh_convergence_plot.png', dpi=300, bbox_inches='tight')
            print("收敛图已保存为: mesh_convergence_plot.png")
        
        plt.show()

def main():
    """主函数"""
    print("="*60)
    print("OpenFOAM 网格敏感性分析工具")
    print("="*60)
    
    analyzer = MeshSensitivityAnalyzer()
    
    # 设置分析方法
    methods = ['mean', 'median', 'weighted', 'total_volume', 'trimmed']
    print(f"可选ACH计算方法: {methods}")
    method = input("选择ACH计算方法 (默认: median): ").strip() or 'median'
    if method in methods:
        analyzer.analysis_method = method
    else:
        print(f"使用默认方法: median")
    
    # 输入网格数据
    while True:
        mesh_name = input("\n输入网格名称 (如: coarse, medium, fine) 或 'done' 完成: ").strip()
        if mesh_name.lower() == 'done':
            break
            
        csv_path = input(f"输入 {mesh_name} 网格的CSV文件路径: ").strip().strip('"')
        
        if os.path.exists(csv_path):
            try:
                df = pd.read_csv(csv_path)
                analyzer.analyze_single_mesh(df, mesh_name)
                print(f"✓ {mesh_name} 网格分析完成")
            except Exception as e:
                print(f"✗ 读取文件失败: {e}")
        else:
            print(f"✗ 文件不存在: {csv_path}")
    
    # 进行比较分析
    if len(analyzer.mesh_results) >= 2:
        comparison_df, convergence_df = analyzer.compare_meshes()
        
        # 绘制收敛图
        plot_choice = input("\n是否绘制收敛图? (y/n): ").strip().lower()
        if plot_choice == 'y':
            analyzer.plot_convergence()
        
        print(f"\n分析完成！生成的文件:")
        print(f"  - mesh_comparison.csv: 网格比较结果")
        print(f"  - mesh_convergence.csv: 收敛性分析")
        if plot_choice == 'y':
            print(f"  - mesh_convergence_plot.png: 收敛图")
    else:
        print("需要至少2个网格进行敏感性分析")

if __name__ == "__main__":
    main()
