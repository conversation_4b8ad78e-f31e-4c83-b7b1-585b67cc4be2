import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def final_mesh_sensitivity_analysis():
    """最终的网格敏感性分析 - 删除Group 4,5后"""
    
    print("="*80)
    print("最终网格敏感性分析报告")
    print("已删除: Group 6(核心筒和走廊), Group 4,5(不属于优化范畴的flat)")
    print("网格精度: ACC=1(粗) → ACC=2(中) → ACC=3(细)")
    print("="*80)
    
    # 读取删除Group 4,5后的收敛性分析结果
    try:
        df = pd.read_csv('three_mesh_convergence_no_group456.csv')
        print(f"✓ 读取收敛性分析数据: {len(df)} 个Group (优化范畴内)")
    except Exception as e:
        print(f"✗ 读取数据失败: {e}")
        return
    
    # 分析各种收敛模式
    convergence_patterns = {
        'excellent_converged': [],       # 优秀收敛 (<2%)
        'good_converged': [],           # 良好收敛 (2-5%)
        'acceptable': [],               # 可接受 (5-15%)
        'needs_refinement': [],         # 需要细化 (15-30%)
        'highly_sensitive': []          # 高敏感性 (>30%)
    }
    
    print(f"\n📊 优化范畴内Group的网格敏感性分析:")
    print(f"{'Group':<6} {'体积':<8} {'粗网格':<10} {'中网格':<10} {'细网格':<10} {'变化%':<8} {'等级':<12} {'建议':<15}")
    print("-" * 95)
    
    for _, row in df.iterrows():
        group_id = int(row['Group'])
        volume = row['Volume'] if pd.notna(row['Volume']) else 0
        
        # 获取ACH值 (粗→中→细)
        ach_1 = row.get('accbuilding_1_no_group456_ACH', np.nan)  # 粗
        ach_2 = row.get('accbuilding_2_no_group456_ACH', np.nan)  # 中
        ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)  # 细
        
        relative_change = row.get('Relative_Change_%', 0)
        converged = row.get('Converged', False)
        
        # 分析收敛等级
        if relative_change < 2:
            level = "优秀✨"
            recommendation = "网格充足"
            convergence_patterns['excellent_converged'].append(group_id)
        elif relative_change < 5:
            level = "良好✅"
            recommendation = "可接受"
            convergence_patterns['good_converged'].append(group_id)
        elif relative_change < 15:
            level = "可接受⚠️"
            recommendation = "建议细化"
            convergence_patterns['acceptable'].append(group_id)
        elif relative_change < 30:
            level = "需细化🔧"
            recommendation = "需要细化"
            convergence_patterns['needs_refinement'].append(group_id)
        else:
            level = "高敏感🚨"
            recommendation = "急需细化"
            convergence_patterns['highly_sensitive'].append(group_id)
        
        print(f"{group_id:<6} {volume:<8.1f} {ach_1:<10.3f} {ach_2:<10.3f} {ach_3:<10.3f} "
              f"{relative_change:<8.2f} {level:<12} {recommendation:<15}")
    
    # 统计各种等级
    print(f"\n📈 收敛等级统计 (优化范畴内):")
    total_groups = len(df)
    
    excellent_count = len(convergence_patterns['excellent_converged'])
    good_count = len(convergence_patterns['good_converged'])
    acceptable_count = len(convergence_patterns['acceptable'])
    needs_refinement_count = len(convergence_patterns['needs_refinement'])
    highly_sensitive_count = len(convergence_patterns['highly_sensitive'])
    
    print(f"  ✨ 优秀收敛 (<2%): {excellent_count} 个 ({excellent_count/total_groups*100:.1f}%)")
    if convergence_patterns['excellent_converged']:
        print(f"     Group: {convergence_patterns['excellent_converged']}")
    
    print(f"  ✅ 良好收敛 (2-5%): {good_count} 个 ({good_count/total_groups*100:.1f}%)")
    if convergence_patterns['good_converged']:
        print(f"     Group: {convergence_patterns['good_converged']}")
    
    print(f"  ⚠️  可接受 (5-15%): {acceptable_count} 个 ({acceptable_count/total_groups*100:.1f}%)")
    if convergence_patterns['acceptable']:
        print(f"     Group: {convergence_patterns['acceptable']}")
    
    print(f"  🔧 需要细化 (15-30%): {needs_refinement_count} 个 ({needs_refinement_count/total_groups*100:.1f}%)")
    if convergence_patterns['needs_refinement']:
        print(f"     Group: {convergence_patterns['needs_refinement']}")
    
    print(f"  🚨 高敏感性 (>30%): {highly_sensitive_count} 个 ({highly_sensitive_count/total_groups*100:.1f}%)")
    if convergence_patterns['highly_sensitive']:
        print(f"     Group: {convergence_patterns['highly_sensitive']}")
    
    # 网格质量评估
    print(f"\n🎯 优化范畴内的网格质量评估:")
    
    satisfactory_groups = excellent_count + good_count
    satisfactory_rate = satisfactory_groups / total_groups * 100
    
    total_acceptable = excellent_count + good_count + acceptable_count
    acceptable_rate = total_acceptable / total_groups * 100
    
    print(f"  总Group数: {total_groups} (优化范畴内)")
    print(f"  满意收敛率 (<5%): {satisfactory_rate:.1f}%")
    print(f"  可接受收敛率 (<15%): {acceptable_rate:.1f}%")
    
    if satisfactory_rate >= 50:
        quality_level = "优秀"
        quality_icon = "✨"
    elif satisfactory_rate >= 30:
        quality_level = "良好"
        quality_icon = "✅"
    elif acceptable_rate >= 60:
        quality_level = "可接受"
        quality_icon = "⚠️"
    else:
        quality_level = "需要改善"
        quality_icon = "🔧"
    
    print(f"  整体网格质量: {quality_icon} {quality_level}")
    
    # 优化建议
    print(f"\n🔧 针对优化范畴的网格细化建议:")
    
    if highly_sensitive_count > 0:
        print(f"  🚨 优先级1 - 高敏感性Group (急需细化):")
        for group_id in convergence_patterns['highly_sensitive']:
            group_data = df[df['Group'] == group_id].iloc[0]
            ach_range = f"{group_data['Min_ACH']:.2f}-{group_data['Max_ACH']:.2f}"
            print(f"     Group {group_id}: ACH变化范围 {ach_range}, 变化{group_data['Relative_Change_%']:.1f}%")
    
    if needs_refinement_count > 0:
        print(f"  🔧 优先级2 - 需要细化Group:")
        for group_id in convergence_patterns['needs_refinement']:
            group_data = df[df['Group'] == group_id].iloc[0]
            print(f"     Group {group_id}: 变化{group_data['Relative_Change_%']:.1f}%，建议细化")
    
    if acceptable_count > 0:
        print(f"  ⚠️  优先级3 - 可接受但建议优化:")
        for group_id in convergence_patterns['acceptable']:
            group_data = df[df['Group'] == group_id].iloc[0]
            print(f"     Group {group_id}: 变化{group_data['Relative_Change_%']:.1f}%，可选择性细化")
    
    # 工程决策建议
    print(f"\n📋 工程决策建议:")
    
    if satisfactory_rate >= 50:
        print(f"  ✅ 当前网格精度对大部分优化区域已足够")
        print(f"  🎯 建议: 对{highly_sensitive_count + needs_refinement_count}个问题Group进行局部细化")
        print(f"  💡 策略: 局部自适应细化，重点关注高敏感性区域")
    elif acceptable_rate >= 60:
        print(f"  ⚠️  当前网格精度基本可接受")
        print(f"  🎯 建议: 优先细化{highly_sensitive_count + needs_refinement_count}个问题Group")
        print(f"  💡 策略: 分阶段细化，先解决高敏感性问题")
    else:
        print(f"  🔧 当前网格精度需要改善")
        print(f"  🎯 建议: 全面细化至ACC=4或更高精度")
        print(f"  💡 策略: 整体网格细化，确保优化区域的计算精度")
    
    # 特殊区域分析
    print(f"\n🔍 特殊区域分析:")
    
    # 分析ACH值分布
    ach_values = []
    for _, row in df.iterrows():
        ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)
        if not np.isnan(ach_3):
            ach_values.append(ach_3)
    
    if ach_values:
        ach_mean = np.mean(ach_values)
        ach_std = np.std(ach_values)
        
        print(f"  ACH值统计 (细网格):")
        print(f"    平均值: {ach_mean:.2f} 次/小时")
        print(f"    标准差: {ach_std:.2f}")
        print(f"    范围: {min(ach_values):.2f} - {max(ach_values):.2f}")
        
        # 识别极值区域
        low_ach_groups = []
        high_ach_groups = []
        
        for _, row in df.iterrows():
            group_id = int(row['Group'])
            ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)
            if not np.isnan(ach_3):
                if ach_3 < ach_mean - ach_std:
                    low_ach_groups.append(group_id)
                elif ach_3 > ach_mean + ach_std:
                    high_ach_groups.append(group_id)
        
        if low_ach_groups:
            print(f"  低通风区域 (ACH < {ach_mean - ach_std:.2f}): Group {low_ach_groups}")
            print(f"    特点: 流动复杂，可能存在死角或回流")
        
        if high_ach_groups:
            print(f"  高通风区域 (ACH > {ach_mean + ach_std:.2f}): Group {high_ach_groups}")
            print(f"    特点: 强对流，湍流强度高")
    
    return convergence_patterns, satisfactory_rate, acceptable_rate

def plot_final_analysis():
    """绘制最终分析图表"""
    
    try:
        df = pd.read_csv('three_mesh_convergence_no_group456.csv')
    except:
        print("无法读取数据文件")
        return
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('优化范畴内的网格敏感性最终分析\n(已删除Group 4,5,6)', fontsize=16, fontweight='bold')
    
    # 1. 收敛等级分布
    ax1 = axes[0, 0]
    
    groups = df['Group'].values
    changes = df['Relative_Change_%'].values
    
    # 按收敛等级分类
    colors = []
    labels = []
    for change in changes:
        if change < 2:
            colors.append('gold')
            labels.append('优秀')
        elif change < 5:
            colors.append('lightgreen')
            labels.append('良好')
        elif change < 15:
            colors.append('lightyellow')
            labels.append('可接受')
        elif change < 30:
            colors.append('lightcoral')
            labels.append('需细化')
        else:
            colors.append('darkred')
            labels.append('高敏感')
    
    bars = ax1.bar(groups, changes, color=colors, alpha=0.8)
    ax1.axhline(y=2, color='gold', linestyle='--', alpha=0.8, label='优秀线(2%)')
    ax1.axhline(y=5, color='green', linestyle='--', alpha=0.8, label='良好线(5%)')
    ax1.axhline(y=15, color='orange', linestyle='--', alpha=0.8, label='可接受线(15%)')
    ax1.axhline(y=30, color='red', linestyle='--', alpha=0.8, label='高敏感线(30%)')
    
    ax1.set_xlabel('Group ID')
    ax1.set_ylabel('相对变化 (%)')
    ax1.set_title('优化范畴内Group的网格敏感性等级')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 收敛等级饼图
    ax2 = axes[0, 1]
    
    excellent = sum(df['Relative_Change_%'] < 2)
    good = sum((df['Relative_Change_%'] >= 2) & (df['Relative_Change_%'] < 5))
    acceptable = sum((df['Relative_Change_%'] >= 5) & (df['Relative_Change_%'] < 15))
    needs_refinement = sum((df['Relative_Change_%'] >= 15) & (df['Relative_Change_%'] < 30))
    highly_sensitive = sum(df['Relative_Change_%'] >= 30)
    
    labels = ['优秀(<2%)', '良好(2-5%)', '可接受(5-15%)', '需细化(15-30%)', '高敏感(≥30%)']
    sizes = [excellent, good, acceptable, needs_refinement, highly_sensitive]
    colors = ['gold', 'lightgreen', 'lightyellow', 'lightcoral', 'darkred']
    
    # 只显示非零的部分
    non_zero_labels = []
    non_zero_sizes = []
    non_zero_colors = []
    
    for i, size in enumerate(sizes):
        if size > 0:
            non_zero_labels.append(labels[i])
            non_zero_sizes.append(size)
            non_zero_colors.append(colors[i])
    
    wedges, texts, autotexts = ax2.pie(non_zero_sizes, labels=non_zero_labels, colors=non_zero_colors, 
                                      autopct='%1.1f%%', startangle=90)
    ax2.set_title('优化范畴内收敛等级分布')
    
    # 3. ACH收敛趋势
    ax3 = axes[1, 0]
    
    # 选择代表性Group展示收敛趋势
    representative_groups = []
    for _, row in df.iterrows():
        group_id = int(row['Group'])
        change = row['Relative_Change_%']
        if change < 5:  # 收敛良好的
            representative_groups.append((group_id, 'converged'))
        elif change > 25:  # 高敏感的
            representative_groups.append((group_id, 'sensitive'))
        
        if len(representative_groups) >= 8:  # 限制显示数量
            break
    
    mesh_levels = [1, 2, 3]
    
    for group_id, group_type in representative_groups:
        group_data = df[df['Group'] == group_id].iloc[0]
        ach_values = [
            group_data.get('accbuilding_1_no_group456_ACH', np.nan),
            group_data.get('accbuilding_2_no_group456_ACH', np.nan),
            group_data.get('accbuilding_3_no_group456_ACH', np.nan)
        ]
        
        if not any(np.isnan(ach_values)):
            color = 'green' if group_type == 'converged' else 'red'
            linestyle = '-' if group_type == 'converged' else '--'
            ax3.plot(mesh_levels, ach_values, 'o-', label=f'Group {group_id}', 
                    linewidth=2, markersize=6, color=color, linestyle=linestyle)
    
    ax3.set_xlabel('网格精度 (1=粗, 2=中, 3=细)')
    ax3.set_ylabel('ACH (次/小时)')
    ax3.set_title('代表性Group的ACH收敛趋势')
    ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax3.grid(True, alpha=0.3)
    ax3.set_xticks([1, 2, 3])
    ax3.set_xticklabels(['ACC=1\n(粗)', 'ACC=2\n(中)', 'ACC=3\n(细)'])
    
    # 4. ACH值分布直方图
    ax4 = axes[1, 1]
    
    ach_values_all = []
    mesh_labels = []
    
    for mesh_col, mesh_name in [('accbuilding_1_no_group456_ACH', 'ACC=1(粗)'),
                               ('accbuilding_2_no_group456_ACH', 'ACC=2(中)'),
                               ('accbuilding_3_no_group456_ACH', 'ACC=3(细)')]:
        if mesh_col in df.columns:
            ach_values = df[mesh_col].dropna().values
            ach_values_all.extend(ach_values)
            mesh_labels.extend([mesh_name] * len(ach_values))
    
    if ach_values_all:
        unique_meshes = ['ACC=1(粗)', 'ACC=2(中)', 'ACC=3(细)']
        colors_hist = ['red', 'orange', 'green']
        
        for i, mesh in enumerate(unique_meshes):
            mesh_ach = [ach_values_all[j] for j, label in enumerate(mesh_labels) if label == mesh]
            if mesh_ach:
                ax4.hist(mesh_ach, bins=12, alpha=0.6, label=mesh, 
                        color=colors_hist[i], density=True)
    
    ax4.set_xlabel('ACH (次/小时)')
    ax4.set_ylabel('密度')
    ax4.set_title('优化范畴内ACH值分布')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('final_mesh_sensitivity_analysis.png', dpi=300, bbox_inches='tight')
    print(f"📊 最终网格敏感性分析图表已保存: final_mesh_sensitivity_analysis.png")

def main():
    """主函数"""
    
    # 执行最终分析
    convergence_patterns, satisfactory_rate, acceptable_rate = final_mesh_sensitivity_analysis()
    
    # 绘制图表
    plot_final_analysis()
    
    print(f"\n💾 最终分析完成！")
    print(f"优化范畴内满意收敛率: {satisfactory_rate:.1f}%")
    print(f"优化范畴内可接受收敛率: {acceptable_rate:.1f}%")

if __name__ == "__main__":
    main()
