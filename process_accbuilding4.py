import pandas as pd
import numpy as np
from scipy import stats

def load_and_clean_data(file_path):
    """加载并清理数据"""
    print(f"正在读取文件: {file_path}")
    
    try:
        df = pd.read_csv(file_path)
        print(f"✓ 成功读取数据: {len(df)} 行")
        print(f"列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return None

def detect_outliers_combined(data, iqr_factor=1.5, z_threshold=3):
    """组合异常值检测方法"""
    if len(data) == 0:
        return np.array([], dtype=bool)
    
    data_array = np.array(data)
    outliers = np.zeros(len(data_array), dtype=bool)
    
    # IQR方法
    Q1 = np.percentile(data_array, 25)
    Q3 = np.percentile(data_array, 75)
    IQR = Q3 - Q1
    lower_bound = Q1 - iqr_factor * IQR
    upper_bound = Q3 + iqr_factor * IQR
    iqr_outliers = (data_array < lower_bound) | (data_array > upper_bound)
    
    # Modified Z-score方法
    median = np.median(data_array)
    mad = np.median(np.abs(data_array - median))
    if mad != 0:
        modified_z_scores = 0.6745 * (data_array - median) / mad
        z_outliers = np.abs(modified_z_scores) > z_threshold
    else:
        z_outliers = np.zeros(len(data_array), dtype=bool)
    
    # 组合判断：两种方法都认为是异常值才标记
    outliers = iqr_outliers & z_outliers
    
    return outliers

def calculate_robust_ach(group_data):
    """计算稳健的ACH值"""
    if len(group_data) == 0:
        return np.nan, np.nan, 0, 0
    
    # 获取流量数据（绝对值）
    flow_rates = np.abs(group_data['FlowRate'].values)
    volumes = group_data['Volume'].values
    
    if len(flow_rates) == 0 or np.all(volumes == 0):
        return np.nan, np.nan, 0, 0
    
    # 异常值检测
    outliers = detect_outliers_combined(flow_rates)
    
    # 清理后的数据
    clean_flow_rates = flow_rates[~outliers]
    clean_volumes = volumes[~outliers]
    
    if len(clean_flow_rates) == 0:
        return np.nan, np.nan, len(outliers), np.sum(outliers)
    
    # 计算总流量（m³/s转换为m³/h）
    total_flow_rate = np.sum(clean_flow_rates) * 3600  # m³/h
    
    # 计算总体积
    total_volume = clean_volumes[0] if len(clean_volumes) > 0 else np.nan
    
    # 计算ACH
    if total_volume > 0:
        ach = total_flow_rate / total_volume
    else:
        ach = np.nan
    
    # 使用中位数方法作为稳健估计
    if len(clean_flow_rates) > 0:
        individual_ach = (clean_flow_rates * 3600) / clean_volumes
        robust_ach = np.median(individual_ach[np.isfinite(individual_ach)])
    else:
        robust_ach = np.nan
    
    return ach, robust_ach, len(flow_rates), np.sum(outliers)

def process_accbuilding4_data():
    """处理ACC=4数据"""
    
    print("="*80)
    print("处理ACC=4数据 (最细网格)")
    print("="*80)
    
    # 读取ACC=4数据
    file_path = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250731_232707.csv"
    df = load_and_clean_data(file_path)
    
    if df is None:
        return
    
    print(f"\n原始数据统计:")
    print(f"总行数: {len(df)}")
    print(f"Group数量: {df['GroupID'].nunique()}")
    print(f"Group范围: {df['GroupID'].min()} - {df['GroupID'].max()}")

    # 移除Group 4, 5, 6 (根据之前的处理逻辑)
    groups_to_remove = [4, 5, 6]
    print(f"\n移除Group: {groups_to_remove}")

    # 统计被移除的数据
    removed_data = df[df['GroupID'].isin(groups_to_remove)]
    print(f"移除的探针点数: {len(removed_data)}")
    if len(removed_data) > 0:
        removed_volume = removed_data.groupby('GroupID')['Volume'].first().sum()
        print(f"移除的总体积: {removed_volume:.1f} m³")

    # 过滤数据
    df_filtered = df[~df['GroupID'].isin(groups_to_remove)].copy()
    print(f"过滤后数据: {len(df_filtered)} 行")
    print(f"剩余Group数量: {df_filtered['GroupID'].nunique()}")
    
    # 按Group计算ACH
    results = []
    
    print(f"\n开始计算各Group的ACH值...")
    print(f"{'Group':<6} {'探针数':<8} {'异常值':<8} {'体积(m³)':<10} {'ACH':<10} {'稳健ACH':<10}")
    print("-" * 60)
    
    for group_id in sorted(df_filtered['GroupID'].unique()):
        group_data = df_filtered[df_filtered['GroupID'] == group_id]
        
        # 计算ACH
        ach, robust_ach, total_probes, outliers_count = calculate_robust_ach(group_data)
        
        # 获取体积
        volume = group_data['Volume'].iloc[0] if len(group_data) > 0 else np.nan
        
        print(f"{group_id:<6} {total_probes:<8} {outliers_count:<8} {volume:<10.1f} {ach:<10.2f} {robust_ach:<10.2f}")
        
        results.append({
            'Group': group_id,
            'Volume': volume,
            'Total_Probes': total_probes,
            'Outliers_Count': outliers_count,
            'ACH': ach,
            'Robust_ACH': robust_ach,
            'Outlier_Rate': outliers_count / total_probes * 100 if total_probes > 0 else 0
        })
    
    # 创建结果DataFrame
    results_df = pd.DataFrame(results)
    
    # 保存清理后的数据
    output_file = 'accbuilding_4_no_group456.csv'
    results_df.to_csv(output_file, index=False)
    print(f"\n✓ ACC=4清理后数据已保存: {output_file}")
    
    # 统计汇总
    print(f"\n📊 ACC=4数据处理汇总:")
    print(f"• 处理的Group数量: {len(results_df)}")
    print(f"• 总探针点数: {results_df['Total_Probes'].sum()}")
    print(f"• 总异常值数: {results_df['Outliers_Count'].sum()}")
    print(f"• 平均异常值率: {results_df['Outlier_Rate'].mean():.1f}%")
    print(f"• 总体积: {results_df['Volume'].sum():.1f} m³")
    print(f"• ACH范围: {results_df['Robust_ACH'].min():.2f} - {results_df['Robust_ACH'].max():.2f}")
    
    # 检查是否有无效的ACH值
    invalid_ach = results_df['Robust_ACH'].isna().sum()
    if invalid_ach > 0:
        print(f"⚠️ 警告: {invalid_ach} 个Group的ACH值无效")
        invalid_groups = results_df[results_df['Robust_ACH'].isna()]['Group'].tolist()
        print(f"无效ACH的Group: {invalid_groups}")
    
    return results_df

def update_four_mesh_convergence_analysis():
    """更新四网格收敛性分析"""
    
    print("\n" + "="*80)
    print("更新四网格收敛性分析")
    print("="*80)
    
    try:
        # 读取现有的三网格数据
        df_3mesh = pd.read_csv('three_mesh_convergence_no_group456.csv')
        print(f"✓ 读取三网格数据: {len(df_3mesh)} 个Group")
        
        # 读取新的ACC=4数据
        df_acc4 = pd.read_csv('accbuilding_4_no_group456.csv')
        print(f"✓ 读取ACC=4数据: {len(df_acc4)} 个Group")
        
        # 合并数据
        merged_df = df_3mesh.merge(df_acc4[['Group', 'Robust_ACH']],
                                  on='Group', how='left',
                                  suffixes=('', '_acc4'))

        # 重命名列
        merged_df = merged_df.rename(columns={'Robust_ACH': 'accbuilding_4_no_group456_ACH'})
        
        # 重新计算收敛性分析
        print(f"\n开始四网格收敛性分析...")
        print(f"{'Group':<6} {'ACC=1':<8} {'ACC=2':<8} {'ACC=3':<8} {'ACC=4':<8} {'3→4%':<8} {'收敛状态':<12}")
        print("-" * 70)
        
        convergence_results = []
        
        for _, row in merged_df.iterrows():
            group_id = int(row['Group'])
            ach_1 = row.get('accbuilding_1_no_group456_ACH', np.nan)
            ach_2 = row.get('accbuilding_2_no_group456_ACH', np.nan)
            ach_3 = row.get('accbuilding_3_no_group456_ACH', np.nan)
            ach_4 = row.get('accbuilding_4_no_group456_ACH', np.nan)
            
            # 计算3→4的变化率
            if not (np.isnan(ach_3) or np.isnan(ach_4)):
                change_3_4 = abs(ach_4 - ach_3) / ach_3 * 100 if ach_3 != 0 else 0
                
                # 判断收敛状态
                if change_3_4 < 1:
                    convergence_status = "完全收敛"
                elif change_3_4 < 3:
                    convergence_status = "良好收敛"
                elif change_3_4 < 5:
                    convergence_status = "可接受"
                else:
                    convergence_status = "需更细网格"
            else:
                change_3_4 = np.nan
                convergence_status = "数据缺失"
            
            print(f"{group_id:<6} {ach_1:<8.2f} {ach_2:<8.2f} {ach_3:<8.2f} {ach_4:<8.2f} {change_3_4:<8.1f} {convergence_status:<12}")
            
            # 添加到结果中
            row_dict = row.to_dict()
            row_dict['Change_3_4_Percent'] = change_3_4
            row_dict['Convergence_Status_4Mesh'] = convergence_status
            convergence_results.append(row_dict)
        
        # 创建四网格结果DataFrame
        four_mesh_df = pd.DataFrame(convergence_results)
        
        # 保存四网格收敛性分析结果
        output_file = 'four_mesh_convergence_no_group456.csv'
        four_mesh_df.to_csv(output_file, index=False)
        print(f"\n✓ 四网格收敛性分析结果已保存: {output_file}")
        
        # 统计汇总
        valid_changes = four_mesh_df['Change_3_4_Percent'].dropna()
        if len(valid_changes) > 0:
            excellent_conv = (valid_changes < 1).sum()
            good_conv = ((valid_changes >= 1) & (valid_changes < 3)).sum()
            acceptable_conv = ((valid_changes >= 3) & (valid_changes < 5)).sum()
            poor_conv = (valid_changes >= 5).sum()
            
            print(f"\n📊 四网格收敛性统计:")
            print(f"• 总Group数: {len(valid_changes)}")
            print(f"• 完全收敛 (<1%): {excellent_conv} ({excellent_conv/len(valid_changes)*100:.1f}%)")
            print(f"• 良好收敛 (1-3%): {good_conv} ({good_conv/len(valid_changes)*100:.1f}%)")
            print(f"• 可接受 (3-5%): {acceptable_conv} ({acceptable_conv/len(valid_changes)*100:.1f}%)")
            print(f"• 需更细网格 (>5%): {poor_conv} ({poor_conv/len(valid_changes)*100:.1f}%)")
            
            # 总体评估
            total_satisfactory = excellent_conv + good_conv
            print(f"• 总体满意率: {total_satisfactory/len(valid_changes)*100:.1f}%")
        
        return four_mesh_df
        
    except Exception as e:
        print(f"✗ 更新四网格分析失败: {e}")
        return None

def main():
    """主函数"""
    
    print("开始处理ACC=4数据...")
    
    # 处理ACC=4数据
    acc4_results = process_accbuilding4_data()
    
    if acc4_results is not None:
        # 更新四网格收敛性分析
        four_mesh_results = update_four_mesh_convergence_analysis()
        
        if four_mesh_results is not None:
            print(f"\n{'='*80}")
            print("ACC=4数据处理完成！")
            print("生成的文件:")
            print("• accbuilding_4_no_group456.csv - ACC=4清理后数据")
            print("• four_mesh_convergence_no_group456.csv - 四网格收敛性分析")
            print(f"{'='*80}")
        else:
            print("四网格收敛性分析失败")
    else:
        print("ACC=4数据处理失败")

if __name__ == "__main__":
    main()
