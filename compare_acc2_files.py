import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def compare_acc2_csv_files():
    """比较两个ACC=2的CSV文件"""
    
    print("="*80)
    print("比较两个ACC=2的CSV文件")
    print("="*80)
    
    # 定义文件路径
    file1_path = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250731_154425.csv"
    file2_path = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250731_204548.csv"
    
    print(f"文件1: {file1_path}")
    print(f"文件2: {file2_path}")
    
    try:
        # 读取两个文件
        print(f"\n📂 读取文件...")
        df1 = pd.read_csv(file1_path)
        df2 = pd.read_csv(file2_path)
        
        print(f"✓ 文件1读取成功: {len(df1)} 行数据")
        print(f"✓ 文件2读取成功: {len(df2)} 行数据")
        
    except Exception as e:
        print(f"✗ 文件读取失败: {e}")
        return
    
    # 基本信息比较
    print(f"\n📊 基本信息比较:")
    print(f"{'项目':<20} {'文件1':<15} {'文件2':<15} {'是否相同':<10}")
    print("-" * 65)
    
    # 行数比较
    rows_same = len(df1) == len(df2)
    print(f"{'行数':<20} {len(df1):<15} {len(df2):<15} {'✅' if rows_same else '❌':<10}")
    
    # 列数比较
    cols_same = len(df1.columns) == len(df2.columns)
    print(f"{'列数':<20} {len(df1.columns):<15} {len(df2.columns):<15} {'✅' if cols_same else '❌':<10}")
    
    # 列名比较
    columns_same = list(df1.columns) == list(df2.columns)
    print(f"{'列名':<20} {'相同' if columns_same else '不同':<15} {'相同' if columns_same else '不同':<15} {'✅' if columns_same else '❌':<10}")
    
    if not columns_same:
        print(f"\n⚠️  列名差异:")
        print(f"  文件1列名: {list(df1.columns)}")
        print(f"  文件2列名: {list(df2.columns)}")
        
        # 找出差异
        cols1_only = set(df1.columns) - set(df2.columns)
        cols2_only = set(df2.columns) - set(df1.columns)
        
        if cols1_only:
            print(f"  仅文件1有: {list(cols1_only)}")
        if cols2_only:
            print(f"  仅文件2有: {list(cols2_only)}")
    
    # 如果基本结构不同，无法进一步比较
    if not (rows_same and cols_same and columns_same):
        print(f"\n❌ 文件结构不同，无法进行详细数据比较")
        return False
    
    # 详细数据比较
    print(f"\n🔍 详细数据比较:")
    
    # 检查是否完全相同
    try:
        files_identical = df1.equals(df2)
        print(f"文件是否完全相同: {'✅ 是' if files_identical else '❌ 否'}")
        
        if files_identical:
            print(f"\n🎉 两个文件完全相同！")
            return True
            
    except Exception as e:
        print(f"比较过程中出错: {e}")
    
    # 如果不完全相同，进行详细分析
    print(f"\n📋 逐列数据比较:")
    
    differences_found = False
    
    for col in df1.columns:
        try:
            if df1[col].dtype in ['float64', 'int64']:
                # 数值列比较
                if df1[col].equals(df2[col]):
                    status = "✅ 相同"
                else:
                    # 检查是否是数值精度问题
                    if np.allclose(df1[col], df2[col], rtol=1e-10, atol=1e-10, equal_nan=True):
                        status = "⚠️  数值精度差异"
                    else:
                        status = "❌ 不同"
                        differences_found = True
                        
                        # 统计差异
                        diff_mask = ~np.isclose(df1[col], df2[col], rtol=1e-10, atol=1e-10, equal_nan=True)
                        diff_count = diff_mask.sum()
                        
                        if diff_count > 0:
                            max_diff = np.abs(df1[col] - df2[col]).max()
                            mean_diff = np.abs(df1[col] - df2[col]).mean()
                            
                            print(f"  {col:<15}: {status} (差异点数: {diff_count}, 最大差异: {max_diff:.6f}, 平均差异: {mean_diff:.6f})")
                        else:
                            print(f"  {col:<15}: {status}")
            else:
                # 非数值列比较
                if df1[col].equals(df2[col]):
                    status = "✅ 相同"
                else:
                    status = "❌ 不同"
                    differences_found = True
                    
                    # 统计差异
                    diff_count = (df1[col] != df2[col]).sum()
                    print(f"  {col:<15}: {status} (差异点数: {diff_count})")
                    
        except Exception as e:
            print(f"  {col:<15}: ❌ 比较失败 ({e})")
    
    # 如果有差异，进行更详细的分析
    if differences_found:
        print(f"\n🔍 差异详细分析:")
        
        # 按Group分析差异
        if 'GroupID' in df1.columns:
            print(f"\n📊 按Group分析差异:")
            
            for group_id in sorted(df1['GroupID'].unique()):
                group1 = df1[df1['GroupID'] == group_id]
                group2 = df2[df2['GroupID'] == group_id]
                
                if len(group1) != len(group2):
                    print(f"  Group {group_id}: 探针数量不同 (文件1: {len(group1)}, 文件2: {len(group2)})")
                    continue
                
                # 检查关键列的差异
                key_columns = ['FlowRate', 'ACH', 'Volume']
                group_has_diff = False
                
                for col in key_columns:
                    if col in df1.columns:
                        if not np.allclose(group1[col], group2[col], rtol=1e-10, atol=1e-10, equal_nan=True):
                            if not group_has_diff:
                                print(f"  Group {group_id}: 存在差异")
                                group_has_diff = True
                            
                            max_diff = np.abs(group1[col] - group2[col]).max()
                            mean_diff = np.abs(group1[col] - group2[col]).mean()
                            print(f"    {col}: 最大差异 {max_diff:.6f}, 平均差异 {mean_diff:.6f}")
        
        # 生成差异报告
        print(f"\n📄 生成差异报告...")
        
        # 找出所有有差异的行
        diff_rows = []
        for idx in range(len(df1)):
            row_has_diff = False
            for col in df1.columns:
                try:
                    if df1[col].dtype in ['float64', 'int64']:
                        if not np.isclose(df1.iloc[idx][col], df2.iloc[idx][col], rtol=1e-10, atol=1e-10, equal_nan=True):
                            row_has_diff = True
                            break
                    else:
                        if df1.iloc[idx][col] != df2.iloc[idx][col]:
                            row_has_diff = True
                            break
                except:
                    continue
            
            if row_has_diff:
                diff_rows.append(idx)
        
        print(f"发现 {len(diff_rows)} 行存在差异 (占总行数的 {len(diff_rows)/len(df1)*100:.2f}%)")
        
        if len(diff_rows) <= 20:  # 如果差异行数不多，显示详细信息
            print(f"\n差异行详情 (前20行):")
            for i, row_idx in enumerate(diff_rows[:20]):
                print(f"  行 {row_idx}: ", end="")
                if 'GroupID' in df1.columns:
                    print(f"Group {df1.iloc[row_idx]['GroupID']}, ", end="")
                
                # 显示主要差异列
                main_diffs = []
                for col in ['FlowRate', 'ACH', 'Volume']:
                    if col in df1.columns:
                        val1 = df1.iloc[row_idx][col]
                        val2 = df2.iloc[row_idx][col]
                        if not np.isclose(val1, val2, rtol=1e-10, atol=1e-10, equal_nan=True):
                            main_diffs.append(f"{col}: {val1:.6f} vs {val2:.6f}")
                
                print(", ".join(main_diffs))
    
    # 总结
    print(f"\n📋 比较总结:")
    if files_identical:
        print(f"✅ 两个文件完全相同")
        return True
    elif not differences_found:
        print(f"✅ 两个文件在数值精度范围内相同")
        return True
    else:
        print(f"❌ 两个文件存在实质性差异")
        print(f"建议检查:")
        print(f"  - 数据生成时间和条件")
        print(f"  - 计算参数设置")
        print(f"  - 网格文件版本")
        print(f"  - 求解器设置")
        return False

def plot_comparison():
    """绘制两个文件的比较图表"""
    
    file1_path = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250731_154425.csv"
    file2_path = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250731_204548.csv"
    
    try:
        df1 = pd.read_csv(file1_path)
        df2 = pd.read_csv(file2_path)
    except:
        print("无法读取文件进行绘图")
        return
    
    if len(df1) != len(df2) or list(df1.columns) != list(df2.columns):
        print("文件结构不同，无法绘制比较图")
        return
    
    # 按Group聚合数据
    if 'GroupID' in df1.columns and 'ACH' in df1.columns:
        group1 = df1.groupby('GroupID')['ACH'].first().reset_index()
        group2 = df2.groupby('GroupID')['ACH'].first().reset_index()
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # ACH值比较
        ax1 = axes[0]
        groups = group1['GroupID']
        ax1.plot(groups, group1['ACH'], 'o-', label='文件1 (154425)', linewidth=2, markersize=6)
        ax1.plot(groups, group2['ACH'], 's--', label='文件2 (204548)', linewidth=2, markersize=6)
        ax1.set_xlabel('Group ID')
        ax1.set_ylabel('ACH (次/小时)')
        ax1.set_title('两个ACC=2文件的ACH值比较')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 差异图
        ax2 = axes[1]
        ach_diff = group2['ACH'] - group1['ACH']
        colors = ['red' if abs(diff) > 0.01 else 'green' for diff in ach_diff]
        bars = ax2.bar(groups, ach_diff, color=colors, alpha=0.7)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.8)
        ax2.set_xlabel('Group ID')
        ax2.set_ylabel('ACH差异 (文件2 - 文件1)')
        ax2.set_title('ACH值差异分布')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('acc2_files_comparison.png', dpi=300, bbox_inches='tight')
        print(f"📊 比较图表已保存: acc2_files_comparison.png")

def main():
    """主函数"""
    
    print("开始比较两个ACC=2的CSV文件...")
    
    # 比较文件
    files_same = compare_acc2_csv_files()
    
    # 绘制比较图
    plot_comparison()
    
    print(f"\n{'='*80}")
    if files_same:
        print("结论: 两个文件相同或在可接受的数值精度范围内相同")
    else:
        print("结论: 两个文件存在差异，需要进一步检查")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
