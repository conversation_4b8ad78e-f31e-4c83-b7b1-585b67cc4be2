import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

def detect_outliers_advanced(data, method='combined', threshold=1.5):
    """高级异常值检测"""
    if len(data) <= 1:
        return np.zeros(len(data), dtype=bool)
    
    outliers = np.zeros(len(data), dtype=bool)
    
    if method == 'iqr':
        Q1 = np.percentile(data, 25)
        Q3 = np.percentile(data, 75)
        IQR = Q3 - Q1
        if IQR > 0:
            lower_bound = Q1 - threshold * IQR
            upper_bound = Q3 + threshold * IQR
            outliers = (data < lower_bound) | (data > upper_bound)
    
    elif method == 'zscore':
        if np.std(data) > 0:
            z_scores = np.abs(stats.zscore(data))
            outliers = z_scores > threshold
    
    elif method == 'modified_zscore':
        median = np.median(data)
        mad = np.median(np.abs(data - median))
        if mad > 0:
            modified_z_scores = 0.6745 * (data - median) / mad
            outliers = np.abs(modified_z_scores) > threshold
    
    elif method == 'combined':
        # 组合方法：同时使用IQR和Modified Z-score
        outliers_iqr = detect_outliers_advanced(data, 'iqr', threshold)
        outliers_mz = detect_outliers_advanced(data, 'modified_zscore', threshold)
        outliers = outliers_iqr | outliers_mz
    
    return outliers

def calculate_ach_robust(group_data, outlier_method='combined', outlier_threshold=1.5):
    """计算robust ACH值"""
    
    flow_rates = group_data['FlowRate'].values
    volume = group_data['Volume'].iloc[0]
    
    # 使用绝对值进行异常值检测
    abs_flow_rates = np.abs(flow_rates)
    
    # 检测异常值
    outliers = detect_outliers_advanced(abs_flow_rates, method=outlier_method, threshold=outlier_threshold)
    outlier_count = np.sum(outliers)
    
    # 确保至少保留一定比例的探针
    min_probes = max(1, int(len(flow_rates) * 0.3))  # 至少保留30%的探针
    if len(flow_rates) - outlier_count < min_probes:
        # 如果异常值太多，使用更宽松的阈值
        outliers = detect_outliers_advanced(abs_flow_rates, method=outlier_method, threshold=outlier_threshold * 1.5)
        outlier_count = np.sum(outliers)
        
        if len(flow_rates) - outlier_count < min_probes:
            # 如果还是太多，只移除最极端的异常值
            sorted_indices = np.argsort(abs_flow_rates)
            keep_count = max(min_probes, len(flow_rates) - int(len(flow_rates) * 0.2))  # 最多移除20%
            outliers = np.ones(len(flow_rates), dtype=bool)
            outliers[sorted_indices[:keep_count]] = False
            outlier_count = np.sum(outliers)
    
    # 清理数据
    clean_flow_rates = flow_rates[~outliers]
    
    # 计算ACH (使用中位数方法)
    Q_median = np.median(np.abs(clean_flow_rates)) * 3600
    ach = Q_median / volume if volume != 0 else 0
    
    return ach, len(clean_flow_rates), outlier_count

def remove_group6_and_clean_data(input_csv_path, output_csv_path, mesh_name):
    """删除Group 6并清理数据"""
    
    print(f"{'='*80}")
    print(f"删除Group 6（核心筒和走廊）并重新处理数据")
    print(f"网格: {mesh_name}")
    print(f"输入文件: {input_csv_path}")
    print(f"输出文件: {output_csv_path}")
    print(f"{'='*80}")
    
    # 读取原始数据
    try:
        df = pd.read_csv(input_csv_path)
        print(f"✓ 成功读取数据，共 {len(df)} 个探针点，{df['GroupID'].nunique()} 个Group")
    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return None, None
    
    # 删除Group 6
    original_groups = df['GroupID'].nunique()
    original_probes = len(df)
    
    # 检查是否存在Group 6
    if 6 in df['GroupID'].values:
        group6_probes = len(df[df['GroupID'] == 6])
        group6_volume = df[df['GroupID'] == 6]['Volume'].iloc[0]
        df_filtered = df[df['GroupID'] != 6].copy()
        print(f"🗑️  删除Group 6: {group6_probes} 个探针点，体积: {group6_volume:.1f} m³")
    else:
        df_filtered = df.copy()
        group6_probes = 0
        print(f"ℹ️  未找到Group 6")
    
    remaining_groups = df_filtered['GroupID'].nunique()
    remaining_probes = len(df_filtered)
    
    print(f"📊 删除统计:")
    print(f"  原始Group数: {original_groups}")
    print(f"  剩余Group数: {remaining_groups}")
    print(f"  原始探针数: {original_probes}")
    print(f"  剩余探针数: {remaining_probes}")
    print(f"  删除探针数: {group6_probes}")
    
    # 按Group处理数据并清理异常值
    grouped = df_filtered.groupby('GroupID')
    cleaned_results = []
    summary_stats = []
    
    print(f"\n🔧 开始数据清理...")
    print(f"{'Group':<6} {'原探针':<8} {'移除':<6} {'保留':<6} {'异常%':<8} {'原ACH':<12} {'新ACH':<12} {'变化%':<10}")
    print("-" * 85)
    
    for group_id, group_data in grouped:
        # 计算新的ACH
        new_ach, valid_probes, outliers_removed = calculate_ach_robust(group_data)
        original_ach = group_data['ACH'].iloc[0] if 'ACH' in group_data.columns else 0
        
        # 计算变化百分比
        if original_ach != 0:
            change_percent = (new_ach - original_ach) / original_ach * 100
        else:
            change_percent = 0
        
        outlier_percentage = outliers_removed / len(group_data) * 100
        
        print(f"{group_id:<6} {len(group_data):<8} {outliers_removed:<6} "
              f"{valid_probes:<6} {outlier_percentage:<8.1f} "
              f"{original_ach:<12.6f} {new_ach:<12.6f} {change_percent:<10.2f}")
        
        # 创建清理后的数据
        flow_rates = group_data['FlowRate'].values
        abs_flow_rates = np.abs(flow_rates)
        outliers = detect_outliers_advanced(abs_flow_rates, 'combined', 1.5)
        
        # 确保至少保留30%的探针
        min_probes = max(1, int(len(group_data) * 0.3))
        if len(group_data) - np.sum(outliers) < min_probes:
            sorted_indices = np.argsort(abs_flow_rates)
            keep_indices = sorted_indices[:min_probes]
            clean_group_data = group_data.iloc[keep_indices].copy()
        else:
            clean_group_data = group_data[~outliers].copy()
        
        # 更新ACH值
        clean_group_data['ACH'] = new_ach
        cleaned_results.append(clean_group_data)
        
        # 保存统计信息
        summary_stats.append({
            'GroupID': group_id,
            'Volume': group_data['Volume'].iloc[0],
            'Total_Probes': len(group_data),
            'Outliers_Removed': outliers_removed,
            'Valid_Probes': valid_probes,
            'Outlier_Percentage': outlier_percentage,
            'Original_ACH': original_ach,
            'New_ACH': new_ach,
            'Change_Percent': change_percent
        })
    
    # 合并清理后的数据
    cleaned_df = pd.concat(cleaned_results, ignore_index=True)
    
    # 保存清理后的CSV
    cleaned_df.to_csv(output_csv_path, index=False)
    
    # 保存统计报告
    summary_df = pd.DataFrame(summary_stats)
    summary_report_path = output_csv_path.replace('.csv', '_processing_report.csv')
    summary_df.to_csv(summary_report_path, index=False)
    
    # 输出总结
    total_original = remaining_probes
    total_cleaned = len(cleaned_df)
    total_removed = total_original - total_cleaned
    removal_rate = total_removed / total_original * 100 if total_original > 0 else 0
    
    print(f"\n📊 处理总结:")
    print(f"  删除Group 6后探针数: {total_original}")
    print(f"  清理后探针数: {total_cleaned}")
    print(f"  异常值移除数: {total_removed}")
    print(f"  异常值移除比例: {removal_rate:.2f}%")
    
    # ACH变化统计
    ach_changes = [s['Change_Percent'] for s in summary_stats if s['Original_ACH'] != 0]
    if ach_changes:
        print(f"  ACH变化统计:")
        print(f"    平均变化: {np.mean(ach_changes):.2f}%")
        print(f"    最大变化: {np.max(np.abs(ach_changes)):.2f}%")
        print(f"    标准差: {np.std(ach_changes):.2f}%")
    
    print(f"\n💾 生成文件:")
    print(f"  - {output_csv_path}: 处理后的数据")
    print(f"  - {summary_report_path}: 处理统计报告")
    
    return cleaned_df, summary_df

def process_both_datasets():
    """处理两个数据集，删除Group 6"""
    
    print("开始处理数据集（删除Group 6 - 核心筒和走廊）...")
    
    # 处理accbuilding_3数据 (删除Group 6)
    print(f"\n{'='*80}")
    print("处理 accbuilding_3 数据")
    print(f"{'='*80}")
    
    acc3_input = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250730_202018.csv"
    acc3_output = "accbuilding_3_no_group6.csv"
    
    acc3_df, acc3_summary = remove_group6_and_clean_data(acc3_input, acc3_output, "accbuilding_3")
    
    # 处理新的accbuilding_2数据 (删除Group 6)
    print(f"\n{'='*80}")
    print("处理 accbuilding_2 数据 (新文件)")
    print(f"{'='*80}")
    
    acc2_input = r"D:\XU\2\DATARECORD\GH\wangge\ach_groups_20250731_154425.csv"
    acc2_output = "accbuilding_2_no_group6.csv"
    
    acc2_df, acc2_summary = remove_group6_and_clean_data(acc2_input, acc2_output, "accbuilding_2")
    
    if acc3_df is not None and acc2_df is not None:
        print(f"\n{'='*80}")
        print("数据处理完成！")
        print("已删除Group 6（核心筒和走廊），保留Group 13")
        print("文件:")
        print(f"  - accbuilding_3_no_group6.csv")
        print(f"  - accbuilding_2_no_group6.csv")
        print(f"{'='*80}")
        
        return True
    else:
        print("数据处理失败！")
        return False

def main():
    """主函数"""
    success = process_both_datasets()
    
    if success:
        print("\n🎯 下一步: 运行网格敏感性分析")
        print("使用处理后的数据文件进行比较分析")
        print("注意: Group 6（核心筒和走廊）已被删除，Group 13保留")

if __name__ == "__main__":
    main()
