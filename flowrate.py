import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

# 读取实际CSV文件（请替换为您的文件路径）
def load_data(csv_path=None):
    """加载探针数据，如果没有提供路径则使用示例数据"""
    if csv_path and pd.io.common.file_exists(csv_path):
        print(f"读取CSV文件: {csv_path}")
        df = pd.read_csv(csv_path)
        return df
    else:
        print("使用示例数据（请替换为实际CSV文件路径）")
        # 示例数据
        data = {
            'GroupID': [0],
            'PointID': [0],
            'FlowRate': [-0.021412659],
            'FlowDir': ['0.000295558944344521,-0.501551048830152,0.0842627137899399'],
            'X': [77.49536133],
            'Y': [17.68268108],
            'Z': [1.799999952],
            'Area': [14.3125],
            'Volume': [50.09375],
            'ACH': [4.824082243]
        }
        return pd.DataFrame(data)

# 异常值检测和处理函数
def detect_outliers(data, method='iqr', threshold=1.5):
    """
    检测异常值
    method: 'iqr', 'zscore', 'modified_zscore'
    """
    if method == 'iqr':
        Q1 = np.percentile(data, 25)
        Q3 = np.percentile(data, 75)
        IQR = Q3 - Q1
        lower_bound = Q1 - threshold * IQR
        upper_bound = Q3 + threshold * IQR
        outliers = (data < lower_bound) | (data > upper_bound)

    elif method == 'zscore':
        z_scores = np.abs(stats.zscore(data))
        outliers = z_scores > threshold

    elif method == 'modified_zscore':
        median = np.median(data)
        mad = np.median(np.abs(data - median))
        modified_z_scores = 0.6745 * (data - median) / mad
        outliers = np.abs(modified_z_scores) > threshold

    return outliers

def clean_probe_data(group_data, outlier_method='iqr', outlier_threshold=1.5, min_probes=1):
    """
    清理探针数据，移除异常值
    """
    flow_rates = group_data['FlowRate'].values
    areas = group_data['Area'].values if 'Area' in group_data.columns else np.ones(len(flow_rates))

    print(f"  原始探针数量: {len(flow_rates)}")
    print(f"  流率范围: [{np.min(flow_rates):.6f}, {np.max(flow_rates):.6f}]")

    # 检测异常值
    abs_flow_rates = np.abs(flow_rates)
    outliers = detect_outliers(abs_flow_rates, method=outlier_method, threshold=outlier_threshold)

    if np.sum(outliers) > 0:
        print(f"  检测到异常值: {np.sum(outliers)} 个")
        print(f"  异常值: {flow_rates[outliers]}")

        # 确保至少保留minimum数量的探针
        if len(flow_rates) - np.sum(outliers) >= min_probes:
            clean_flow_rates = flow_rates[~outliers]
            clean_areas = areas[~outliers]
            print(f"  移除异常值后探针数量: {len(clean_flow_rates)}")
        else:
            print(f"  警告: 移除异常值后探针数量不足，保留所有数据")
            clean_flow_rates = flow_rates
            clean_areas = areas
    else:
        print(f"  未检测到异常值")
        clean_flow_rates = flow_rates
        clean_areas = areas

    return clean_flow_rates, clean_areas

# 计算ACH的robust方法（处理异常值）
def calculate_ach_robust(df, outlier_method='iqr', outlier_threshold=1.5):
    """
    计算ACH，包含异常值处理和多种计算方法
    """
    grouped = df.groupby('GroupID')
    results = []

    for group_id, group_data in grouped:
        print(f"\n{'='*50}")
        print(f"Group {group_id} 分析:")
        print(f"{'='*50}")

        # 获取基本数据
        volume = group_data['Volume'].iloc[0]
        csv_ach = group_data['ACH'].iloc[0] if 'ACH' in group_data.columns else None

        print(f"体积: {volume:.3f} m³")
        if csv_ach is not None:
            print(f"CSV中的ACH: {csv_ach:.6f}")

        # 清理探针数据
        clean_flow_rates, clean_areas = clean_probe_data(
            group_data, outlier_method, outlier_threshold
        )

        # 计算不同方法的ACH
        methods_results = {}

        # 方法1: 平均绝对流率
        Q1 = np.mean(np.abs(clean_flow_rates)) * 3600
        ACH1 = Q1 / volume if volume != 0 else 0
        methods_results['平均绝对流率'] = {'Q': Q1, 'ACH': ACH1}

        # 方法2: 加权平均流率（按面积加权）
        if len(clean_areas) == len(clean_flow_rates):
            weighted_flow = np.average(np.abs(clean_flow_rates), weights=clean_areas)
            Q2 = weighted_flow * 3600
            ACH2 = Q2 / volume if volume != 0 else 0
            methods_results['面积加权平均'] = {'Q': Q2, 'ACH': ACH2}

        # 方法3: 总体积流率（流率×面积的总和）
        if len(clean_areas) == len(clean_flow_rates):
            Q3 = np.sum(np.abs(clean_flow_rates * clean_areas)) * 3600
            ACH3 = Q3 / volume if volume != 0 else 0
            methods_results['总体积流率'] = {'Q': Q3, 'ACH': ACH3}

        # 方法4: 中位数方法（更robust）
        Q4 = np.median(np.abs(clean_flow_rates)) * 3600
        ACH4 = Q4 / volume if volume != 0 else 0
        methods_results['中位数方法'] = {'Q': Q4, 'ACH': ACH4}

        # 方法5: 截断平均（去除极值后平均）
        sorted_flows = np.sort(np.abs(clean_flow_rates))
        trim_percent = 0.1  # 去除10%的极值
        trim_count = int(len(sorted_flows) * trim_percent)
        if trim_count > 0 and len(sorted_flows) > 2 * trim_count:
            trimmed_flows = sorted_flows[trim_count:-trim_count]
        else:
            trimmed_flows = sorted_flows
        Q5 = np.mean(trimmed_flows) * 3600
        ACH5 = Q5 / volume if volume != 0 else 0
        methods_results['截断平均'] = {'Q': Q5, 'ACH': ACH5}

        # 输出所有方法的结果
        print(f"\nACH计算结果:")
        print(f"{'方法':<12} {'流率(m³/h)':<12} {'ACH':<10} {'与CSV误差%':<12}")
        print("-" * 50)

        best_method = None
        best_error = float('inf')
        recommended_ach = None

        for method_name, result in methods_results.items():
            Q, ACH = result['Q'], result['ACH']
            if csv_ach is not None:
                error = abs(ACH - csv_ach) / csv_ach * 100
                if error < best_error:
                    best_error = error
                    best_method = method_name
                    recommended_ach = ACH
                error_str = f"{error:.2f}%"
            else:
                error_str = "N/A"

            print(f"{method_name:<12} {Q:<12.6f} {ACH:<10.6f} {error_str:<12}")

        # 如果没有CSV参考值，推荐使用中位数方法（最robust）
        if csv_ach is None:
            recommended_ach = methods_results['中位数方法']['ACH']
            best_method = '中位数方法'
            print(f"\n推荐使用: {best_method} (最robust方法)")
        else:
            print(f"\n最接近CSV的方法: {best_method} (误差: {best_error:.2f}%)")

        # 网格敏感性分析建议
        print(f"\n网格敏感性分析建议:")
        print(f"  推荐ACH值: {recommended_ach:.6f}")
        print(f"  计算方法: {best_method}")
        print(f"  有效探针数: {len(clean_flow_rates)}")

        # 存储结果
        result_dict = {
            'GroupID': group_id,
            'Volume': volume,
            'Valid_Probes': len(clean_flow_rates),
            'Recommended_ACH': recommended_ach,
            'Recommended_Method': best_method,
        }

        # 添加所有方法的结果
        for method_name, result in methods_results.items():
            clean_method_name = method_name.replace(' ', '_')
            result_dict[f'{clean_method_name}_ACH'] = result['ACH']
            result_dict[f'{clean_method_name}_Q'] = result['Q']

        if csv_ach is not None:
            result_dict['CSV_ACH'] = csv_ach
            result_dict['Best_Error_Percent'] = best_error

        results.append(result_dict)

    return pd.DataFrame(results)

def main():
    """主函数：网格敏感性分析的ACH计算"""
    print("="*60)
    print("网格敏感性分析 - ACH计算工具")
    print("="*60)

    # 加载数据
    csv_path = input("请输入CSV文件路径（直接回车使用示例数据）: ").strip().strip('"')
    df = load_data(csv_path if csv_path else None)

    print(f"\n数据概览:")
    print(f"总探针数: {len(df)}")
    print(f"Group数量: {df['GroupID'].nunique()}")
    print(f"数据列: {list(df.columns)}")

    # 异常值检测参数设置
    print(f"\n异常值检测设置:")
    outlier_methods = ['iqr', 'zscore', 'modified_zscore']
    print(f"可选方法: {outlier_methods}")

    method = input("选择异常值检测方法 (默认: iqr): ").strip() or 'iqr'
    if method not in outlier_methods:
        method = 'iqr'
        print(f"使用默认方法: {method}")

    threshold_input = input("异常值阈值 (默认: 1.5): ").strip()
    try:
        threshold = float(threshold_input) if threshold_input else 1.5
    except ValueError:
        threshold = 1.5
        print(f"使用默认阈值: {threshold}")

    # 计算ACH
    result_df = calculate_ach_robust(df, outlier_method=method, outlier_threshold=threshold)

    # 输出结果摘要
    print(f"\n{'='*60}")
    print("网格敏感性分析结果摘要:")
    print(f"{'='*60}")

    summary_columns = ['GroupID', 'Volume', 'Valid_Probes', 'Recommended_ACH', 'Recommended_Method']
    if 'CSV_ACH' in result_df.columns:
        summary_columns.extend(['CSV_ACH', 'Best_Error_Percent'])

    print(result_df[summary_columns].to_string(index=False))

    # 保存详细结果
    output_file = 'mesh_sensitivity_ach_analysis.csv'
    result_df.to_csv(output_file, index=False)
    print(f"\n详细结果已保存到: {output_file}")

    # 网格敏感性分析建议
    print(f"\n{'='*60}")
    print("网格敏感性分析建议:")
    print(f"{'='*60}")

    for _, row in result_df.iterrows():
        group_id = row['GroupID']
        ach = row['Recommended_ACH']
        method = row['Recommended_Method']
        probes = row['Valid_Probes']

        print(f"\nGroup {group_id}:")
        print(f"  推荐ACH值: {ach:.6f}")
        print(f"  计算方法: {method}")
        print(f"  有效探针数: {probes}")

        if probes < 5:
            print(f"  ⚠️  警告: 有效探针数较少，建议增加探针密度")

        # ACH合理性检查
        if ach < 0.5:
            print(f"  ⚠️  注意: ACH值较低，可能通风不足")
        elif ach > 50:
            print(f"  ⚠️  注意: ACH值较高，请检查计算是否正确")
        else:
            print(f"  ✓ ACH值在合理范围内")

    print(f"\n网格敏感性分析步骤:")
    print(f"1. 使用不同网格密度重新计算")
    print(f"2. 比较各网格的ACH值变化")
    print(f"3. 当ACH变化<5%时，认为网格收敛")
    print(f"4. 推荐使用{method}方法进行一致性比较")

if __name__ == "__main__":
    main()